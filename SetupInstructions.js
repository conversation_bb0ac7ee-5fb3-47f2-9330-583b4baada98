/**
 * To use this script, you must perform the following setup steps:
 *
 * 1. ADD THE FIRESTORE LIBRARY:
 *    - In the Apps Script editor, click the '+' icon next to "Libraries".
 *    - Script ID: 1WyoVOxSyrRQALgOzTFD_0XLiGfMhoV06rGqQGHKd8Ljen9o99mvxVa91
 *    - Identifier: FirestoreApp
 *    - Version: Select the latest version.
 *
 * 2. ADD THE STRUCTURED LOGGING LIBRARY:
 *    - In the Apps Script editor, click the '+' icon next to "Libraries".
 *    - Script ID: 1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u
 *    - Identifier: Logger
 *    - Version: Select the latest version (currently 5).
 *    - After adding, run `configureLogger()` function (you'll need to create this in a separate file, e.g., `Logging.js`) once to link your script to your GCP project for logging.
 *
 * 3. CONFIGURE GOOGLE CLOUD SERVICE ACCOUNT & SCOPES:
 *    - Ensure your Apps Script project is linked to a Google Cloud Project.
 *    - In your GCP Console, go to "IAM & Admin" > "Service Accounts".
 *    - Create a new service account or use an existing one.
 *    - Grant it the "Cloud Datastore User" role (or "Cloud Datastore Owner" if needed).
 *    - The service account email will be used in the `saveChunksToFirestore` function.
 *    - Ensure the `appsscript.json` manifest includes the following OAuth scopes:
 *      - `https://www.googleapis.com/auth/iam`
 *      - `https://www.googleapis.com/auth/script.external_request`
 *      - `https://www.googleapis.com/auth/datastore` (or `https://www.googleapis.com/auth/cloud-platform`)
 *      - `https://www.googleapis.com/auth/logging.write`
 *      - `https://www.googleapis.com/auth/userinfo.email`
 *    - IMPORTANT: After updating `appsscript.json` or if you encounter a "Permission denied" error for `UrlFetchApp.fetch`, you MUST re-authorize your script in the Apps Script editor. To do this, run any function (e.g., `testPhase1` or `runFullProcessTest`) and grant the requested permissions when prompted.
 *
 * 4. STORE FIRESTORE PROJECT ID SECURELY:
 *    - In the Apps Script editor, go to "Project Settings" > "Script Properties".
 *    - Add a new property:
 *      - `firestore_project_id` (Your Google Cloud Project ID)
 *    - Paste the corresponding value for this property.
 */
