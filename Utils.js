/**
 * Validates if a given string can be successfully parsed into a valid Date object.
 * This is crucial for ensuring reliable date-based operations on message timestamps.
 *
 * @param {string} dateString The string to validate, expected to be in an ISO 8601 compatible format.
 * @returns {boolean} True if the string represents a valid date, false otherwise.
 */
function isValidDateString(dateString) {
  if (!dateString || typeof dateString !== 'string') {
    return false;
  }
  const date = new Date(dateString);
  return !isNaN(date.getTime()); // Check if the date is valid (not "Invalid Date")
}

/**
 * Fetches user details from cache or the People API.
 * @private
 * @param {string} userResourceName The "users/{user}" string from the Chat API.
 * @param {Map<string, object>} executionCache A cache for the current run.
 * @return {{name: string, displayName: string, email: string|null}} An object with user details.
 */
function getUserDetails(userResourceName, executionCache) {
    const NOT_FOUND_SENTINEL = '__NOT_FOUND__';
    const defaultUserDetails = {
        name: userResourceName,
        displayName: userResourceName, // Default to resource name
        email: null,
    };

    if (!userResourceName || !userResourceName.startsWith('users/')) {
        return { name: userResourceName, displayName: 'App', email: null };
    }

    if (executionCache.has(userResourceName)) {
        const cached = executionCache.get(userResourceName);
        return cached === NOT_FOUND_SENTINEL ? defaultUserDetails : cached;
    }

    try {
        const peopleApiResourceName = userResourceName.replace(/^users\//, 'people/');
        const person = People.People.get(peopleApiResourceName, { personFields: 'names,emailAddresses' });

        const displayName = person.names && person.names.length > 0 ? person.names[0].displayName : null;
        const email = person.emailAddresses && person.emailAddresses.length > 0 ? person.emailAddresses[0].value : null;

        if (!displayName) {
            executionCache.set(userResourceName, NOT_FOUND_SENTINEL);
            return defaultUserDetails;
        }

        const userDetails = { name: userResourceName, displayName: displayName, email: email };
        executionCache.set(userResourceName, userDetails);
        return userDetails;

    } catch (e) {
        Log.warn(`Could not fetch user details for ${userResourceName}: ${e.message}`);
        executionCache.set(userResourceName, NOT_FOUND_SENTINEL);
        // When the API call fails, return the resource name as the display name
        return {
            name: userResourceName,
            displayName: userResourceName,
            email: null,
        };
    }
}
