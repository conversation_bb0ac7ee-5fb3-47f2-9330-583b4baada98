// This file serves as the main entry point for the Google Apps Script project.
// It orchestrates the functions defined in other logical files.

// The main function to process chat messages.
// It relies on functions defined in ChatProcessor.js, FirestoreService.js, ChatHistoryService.js,
// and configuration constants from Config.js.

/**
 * Orchestrates the fetching, processing, and storing of chat history for a configured space.
 * This function serves as the main entry point for the script's execution.
 * It can be run manually from the Apps Script editor or configured as a time-driven trigger.
 * @returns {object} A summary report of the execution.
 * @public
 */
function processChatSpace() {
  const report = {
    spaceId: SPACE_NAME,
    messagesFetched: 0,
    clusteredSummariesSaved: 0,
    lastProcessedTimestampUpdated: false,
    newLastProcessedTimestamp: null,
    status: "Error", // Default status
    error: null
  };

  Log.info("Starting processChatSpace execution.");

  const spaceId = SPACE_NAME; // Get the space ID from Config.js
  if (!spaceId) {
    const errorMessage = "SPACE_NAME is not configured in Config.js. Please set it to a valid space ID.";
    Log.error(errorMessage);
    report.error = errorMessage;
    return report;
  }
  report.spaceId = spaceId;

  // 1. Get the last processed timestamp for this space
  const lastProcessedTimestamp = getLastProcessedTimestamp(spaceId);
  Log.info(`Last processed timestamp for ${spaceId}: ${lastProcessedTimestamp || 'None'}`);

  // 2. Fetch new messages from the chat space
  let messages;
  try {
    messages = getChatHistory(spaceId, LOOKBACK_DAYS, lastProcessedTimestamp);
    report.messagesFetched = messages.length;
    Log.info(`Fetched ${messages.length} new messages from ${spaceId}.`);
  } catch (e) {
    const errorMessage = `Failed to fetch chat history for ${spaceId}: ${e.message}`;
    Log.critical(errorMessage, { stack: e.stack });
    report.error = errorMessage;
    return report;
  }

  if (messages.length === 0) {
    Log.info(`No new messages to process for ${spaceId}.`);
    report.status = "No new messages";
    return report;
  }

  // 3. Process and store the fetched messages (now performs clustering and summarization)
  const processingReport = processChatHistoryAndStore(messages, FIRESTORE_COLLECTION_NAME, CLUSTERED_COLLECTION_NAME);
  report.clusteredSummariesSaved = processingReport.saveReport.successful;
  Log.info(`Chat history processing complete for ${spaceId}. Clustered summaries saved: ${report.clusteredSummariesSaved}`);

  // 4. Update the last processed timestamp with the most recent message's timestamp that was actually saved
  if (processingReport.clusteredSummaries && processingReport.clusteredSummaries.length > 0) {
    let latestProcessedTimestamp = null;
    // Create a map for efficient lookup of messages by their name (ID)
    const messageMap = new Map(messages.map(msg => [msg.name, msg]));

    for (const summary of processingReport.clusteredSummaries) {
      for (const messageId of summary.message_ids) {
        const originalMessage = messageMap.get(messageId);
        if (originalMessage && originalMessage.createTime) {
          const currentMessageTime = new Date(originalMessage.createTime).getTime();
          if (latestProcessedTimestamp === null || currentMessageTime > new Date(latestProcessedTimestamp).getTime()) {
            latestProcessedTimestamp = originalMessage.createTime;
          }
        }
      }
    }

    if (latestProcessedTimestamp) {
      const updateSuccess = updateLastProcessedTimestamp(spaceId, latestProcessedTimestamp);
      report.lastProcessedTimestampUpdated = updateSuccess;
      if (updateSuccess) {
        report.newLastProcessedTimestamp = latestProcessedTimestamp;
        Log.info(`Updated last processed timestamp for ${spaceId} to: ${latestProcessedTimestamp} (based on processed messages).`);
      } else {
        const errorMessage = `Failed to update last processed timestamp for ${spaceId}.`;
        Log.error(errorMessage);
        report.error = report.error ? `${report.error}; ${errorMessage}` : errorMessage;
      }
    } else {
      const warningMessage = `No valid timestamps found in newly processed messages for ${spaceId}. Last processed timestamp not updated.`;
      Log.warn(warningMessage);
      report.error = report.error ? `${report.error}; ${warningMessage}` : warningMessage;
    }
  } else {
    Log.info(`No new clustered summaries were generated for ${spaceId}. Last processed timestamp not updated.`);
    report.lastProcessedTimestampUpdated = false;
  }

  if (!report.error) {
    report.status = "Success";
  } else if (report.status === "Error" && !report.error) {
    // If status is still "Error" but no specific error message was set,
    // it means an early return happened without setting a detailed error.
    report.error = "An unexpected error occurred during processing.";
  }

  Log.info("processChatSpace execution finished.", { report: report });
  return report;
}

/**
 * Exports the clustered chat summaries to a new Google Document.
 * This function retrieves the summaries from Firestore and formats them into a readable document.
 *
 * @returns {string} The URL of the newly created Google Document.
 * @public
 */
function exportSummariesToGoogleDoc() {
  Log.info("Initiating export of clustered summaries to Google Doc.");
  try {
    const docUrl = exportClusteredSummariesToDoc(SPACE_NAME);
    Log.info(`Successfully exported summaries to Google Doc: ${docUrl}`);
    return docUrl;
  } catch (e) {
    const errorMessage = `Failed to export summaries to Google Doc: ${e.message}`;
    Log.error(errorMessage, { stack: e.stack });
    throw new Error(errorMessage);
  }
}

/**
 * Clears all production chat data (raw messages, clustered summaries, and processing metadata)
 * from Firestore. This function is intended for administrative purposes and should be used with extreme caution.
 * It uses the production collection names defined in Config.js.
 * @returns {object} A report detailing the outcome of the deletion operation.
 * @public
 */
function clearProductionData() {
  Log.warn("ATTENTION: Initiating deletion of ALL PRODUCTION CHAT DATA. This action is irreversible.");
  const report = deleteChatSpaceData(SPACE_NAME); // Use the configured production space name
  if (report.status === "Success") {
    Log.info("Successfully cleared all production chat data.");
  } else {
    Log.error("Failed to clear all production chat data. Review logs for details.", { report: report });
  }
  return report;
}

/**
 * Deletes all chat data (raw messages, clustered summaries, and processing metadata)
 * associated with a specific chat space from Firestore.
 * This function is intended for administrative purposes, such as resetting a space's data.
 *
 * @param {string} [spaceId=SPACE_NAME] The ID of the chat space for which to delete data.
 *   If not provided, it defaults to the `SPACE_NAME` from Config.js.
 * @returns {object} A report detailing the outcome of the deletion operation.
 * @public
 */
function deleteChatSpaceData(spaceId = SPACE_NAME) {
  const report = {
    spaceId: spaceId,
    rawChunksCleared: false,
    clusteredSummariesCleared: false,
    summarizedChunksCleared: false, // Add this new field
    metadataCleared: false,
    status: "Error",
    error: null
  };

  if (!spaceId) {
    const errorMessage = "Space ID is not provided and SPACE_NAME is not configured in Config.js. Cannot delete data.";
    Log.error(errorMessage);
    report.error = errorMessage;
    return report;
  }

  Log.info(`Starting deletion of chat data for space: ${spaceId}`);

  // Delete raw chat chunks (if still used)
  try {
    report.rawChunksCleared = clearFirestoreCollection_(FIRESTORE_COLLECTION_NAME);
    if (report.rawChunksCleared) {
      Log.info(`Successfully cleared raw chat messages for space: ${spaceId}`);
    } else {
      throw new Error(`Failed to clear raw chat messages for space: ${spaceId}`);
    }
  } catch (e) {
    const errorMessage = `Error clearing raw chat messages: ${e.message}`;
    Log.error(errorMessage, { stack: e.stack });
    report.error = report.error ? `${report.error}; ${errorMessage}` : errorMessage;
  }

  // Delete clustered chat summaries
  try {
    report.clusteredSummariesCleared = clearFirestoreCollection_(CLUSTERED_COLLECTION_NAME);
    if (report.clusteredSummariesCleared) {
      Log.info(`Successfully cleared clustered chat summaries for space: ${spaceId}`);
    } else {
      throw new Error(`Failed to clear clustered chat summaries for space: ${spaceId}`);
    }
  } catch (e) {
    const errorMessage = `Error clearing clustered chat summaries: ${e.message}`;
    Log.error(errorMessage, { stack: e.stack });
    report.error = report.error ? `${report.error}; ${errorMessage}` : errorMessage;
  }

  // Delete summarized chat chunks
  try {
    report.summarizedChunksCleared = clearFirestoreCollection_('summarized-chat-chunks');
    if (report.summarizedChunksCleared) {
      Log.info(`Successfully cleared summarized chat chunks for space: ${spaceId}`);
    } else {
      throw new Error(`Failed to clear summarized chat chunks for space: ${spaceId}`);
    }
  } catch (e) {
    const errorMessage = `Error clearing summarized chat chunks: ${e.message}`;
    Log.error(errorMessage, { stack: e.stack });
    report.error = report.error ? `${report.error}; ${errorMessage}` : errorMessage;
  }

  // Delete metadata (last processed timestamp) for the space
  try {
    report.metadataCleared = clearFirestoreCollection_(METADATA_COLLECTION_NAME); // Use clearFirestoreCollection_ for metadata collection
    if (report.metadataCleared) {
      Log.info(`Successfully cleared metadata for space: ${spaceId}`);
    } else {
      throw new Error(`Failed to clear metadata for space: ${spaceId}`);
    }
  } catch (e) {
    const errorMessage = `Error clearing metadata: ${e.message}`;
    Log.error(errorMessage, { stack: e.stack });
    report.error = report.error ? `${report.error}; ${errorMessage}` : errorMessage;
  }

  if (!report.error) {
    report.status = "Success";
    Log.info(`Successfully deleted all chat data for space: ${spaceId}`, { report: report });
  } else {
    report.status = "Partial Success / Error";
    Log.error(`Finished deletion with errors for space: ${spaceId}`, { report: report });
  }

  return report;
}
