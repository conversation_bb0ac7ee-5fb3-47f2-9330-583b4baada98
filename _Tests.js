// ------------------- TEST FUNCTIONS -------------------

const TEST_SPACE_ID = 'spaces/TEST_SPACE_ID'; // A dummy space ID for testing purposes

/**
 * Clears all test data from Firestore collections and metadata.
 * This function should be run before or after tests to ensure a clean state.
 * @public
 */
function clearTestData() {
  Log.info("--- Clearing Test Data from Firestore ---");
  try {
    const clearedRaw = clearFirestoreCollection(TEST_FIRESTORE_COLLECTION_NAME);
    const clearedClustered = clearFirestoreCollection(TEST_CLUSTERED_COLLECTION_NAME);
    const clearedMetadata = deleteMetadataForSpace(TEST_SPACE_ID);

    if (clearedRaw && clearedClustered && clearedMetadata) {
      Log.info("Successfully cleared all test data.");
    } else {
      Log.warn("Failed to clear all test data. Check logs for details.");
    }
  } catch (e) {
    Log.error(`Error during test data cleanup: ${e.message}`, { stack: e.stack });
  }
  Log.info("--- Test Data Cleanup Complete ---");
}

/**
 * Test-specific version of processChatHistoryAndStore that bypasses the "already processed" filter.
 * This ensures we test the full pipeline even if messages might already exist in Firestore.
 */
function processChatHistoryAndStoreForTest_(messages, rawCollectionName, clusteredCollectionName) {
  if (!messages || messages.length === 0) {
    Log.warn("No messages provided for processing.");
    return { clusteredSummaries: [], saveReport: { successful: 0, failed: 0, errors: [] } };
  }

  Log.info(`Processing ${messages.length} messages for testing (bypassing already-processed filter).`);

  // Step 1: Prepare messages for clustering (skip the "already processed" check for testing)
  const messagesForClustering = messages.map(message => ({
    id: message.name,
    text: message.text || ''
  }));
  Log.info(`Prepared ${messagesForClustering.length} messages for clustering.`);

  // Step 2: Perform large-scale topic clustering using the Gemini service
  const clusteredResults = clusterLargeDataset(messagesForClustering);

  if (!clusteredResults || clusteredResults.length === 0) {
    Log.error("No clusters were generated by the Gemini service.");
    return { clusteredSummaries: [], saveReport: { successful: 0, failed: 0, errors: [] } };
  }

  Log.info(`Generated ${clusteredResults.length} clusters from Gemini service.`);

  // Step 3: Generate summaries for each cluster
  const clusteredSummaries = [];
  for (const cluster of clusteredResults) {
    const clusterMessages = messages.filter(msg => cluster.message_ids.includes(msg.name));
    const conversationText = clusterMessages.map(message => {
      const senderDisplayName = message.sender ? message.sender.displayName : 'Unknown';
      const timestamp = message.createTime || '';
      const text = message.text || '';
      return `${senderDisplayName} (${timestamp}): ${text}`;
    }).join('\n');

    let clusterSummary = "No summary generated.";
    try {
      clusterSummary = getGeminiSummaryForCluster_(conversationText, cluster.cluster_name);
      Log.debug(`Summarized cluster "${cluster.cluster_name}".`);
    } catch (e) {
      Log.error(`Failed to summarize cluster "${cluster.cluster_name}": ${e.message}`);
      clusterSummary = `Summarization failed for this cluster: ${e.message}`;
    }

    clusteredSummaries.push({
      cluster_id: cluster.cluster_id,
      cluster_name: cluster.cluster_name,
      summary: clusterSummary,
      message_ids: cluster.message_ids,
      message_count: cluster.message_ids.length,
      summarizedAt: new Date().toISOString()
    });
  }
  Log.info(`Generated summaries for ${clusteredSummaries.length} clusters.`);

  // Step 4: Save to Firestore
  const saveReport = saveChunksToFirestore(clusteredSummaries, clusteredCollectionName);
  Log.info(`Save report: Successful: ${saveReport.successful}, Failed: ${saveReport.failed}`);

  return {
    clusteredSummaries: clusteredSummaries,
    saveReport: saveReport
  };
}

/**
 * Test function for the full process with Firestore Integration.
 * This function uses mock data to run the entire clustering and saving process.
 * Tests the complete pipeline: message processing -> clustering -> summarization -> Firestore storage.
 * Run this function in the Apps Script editor to see the output in the logs.
 */
function runFullProcessTest() {
  Log.info("--- Starting Full Process Test ---");
  Log.info("Testing the complete chat processing pipeline with mock data...");

  // Validate test environment
  if (!GEMINI_API_KEY || !FIRESTORE_PROJECT_ID) {
    Log.error("FAIL: Missing required configuration. Please ensure GEMINI_API_KEY and FIRESTORE_PROJECT_ID are set in Config.js");
    return { success: false, error: "Missing configuration" };
  }

  clearTestData(); // Clear previous test data before running

  // Enhanced mock messages with more realistic content and proper sender structure
  const mockMessages = [
    // Threaded messages for thread-1 (Project Planning Discussion)
    { name: "msg/1", sender: { displayName: "Alice", name: "users/alice" }, createTime: "2025-08-01T10:00:00.000Z", text: "Hello team! We need to discuss the new project timeline and resource allocation. Please come prepared with your estimates.", thread: { name: "spaces/A/threads/thread-1" } },
    { name: "msg/2", sender: { displayName: "Bob", name: "users/bob" }, createTime: "2025-08-01T10:01:00.000Z", text: "Hi Alice! I've got some initial numbers for the backend development. It looks like we'll need an extra week for testing.", thread: { name: "spaces/A/threads/thread-1" } },
    { name: "msg/3", sender: { displayName: "Alice", name: "users/alice" }, createTime: "2025-08-01T10:02:00.000Z", text: "Thanks Bob. Charlie, what about the frontend? Any blockers there?", thread: { name: "spaces/A/threads/thread-1" } },
    { name: "msg/4", sender: { displayName: "Charlie", name: "users/charlie" }, createTime: "2025-08-01T10:03:00.000Z", text: "Frontend is on track, but we might need to re-evaluate the UI/UX for mobile. It's not as responsive as we hoped.", thread: { name: "spaces/A/threads/thread-1" } },

    // Threaded messages for thread-2 (Technical Incident)
    { name: "msg/5", sender: { displayName: "David", name: "users/david" }, createTime: "2025-08-01T11:00:00.000Z", text: "Urgent: Server outage detected in US-East-1. Investigating root cause now. All services impacted.", thread: { name: "spaces/A/threads/thread-2" } },
    { name: "msg/6", sender: { displayName: "Eve", name: "users/eve" }, createTime: "2025-08-01T11:05:00.000Z", text: "Acknowledged. What's the estimated time to recovery? Do we need to notify customers?", thread: { name: "spaces/A/threads/thread-2" } },
    { name: "msg/7", sender: { displayName: "David", name: "users/david" }, createTime: "2025-08-01T11:10:00.000Z", text: "Working on it. Initial assessment points to a database connection issue. Customer notification is being drafted.", thread: { name: "spaces/A/threads/thread-2" } },

    // Orphaned messages - Group 1 (Marketing Strategy Discussion)
    { name: "msg/8", sender: { displayName: "Frank", name: "users/frank" }, createTime: "2025-08-01T12:00:00.000Z", text: "Just a quick note on the project scope. Are we still aiming for a Q3 launch?", thread: {} },
    { name: "msg/9", sender: { displayName: "Grace", name: "users/grace" }, createTime: "2025-08-01T12:05:00.000Z", text: "Yes, Q3 is still the target. We need to finalize the marketing strategy by end of next week.", thread: {} },
    { name: "msg/10", sender: { displayName: "Heidi", name: "users/heidi" }, createTime: "2025-08-01T12:10:00.000Z", text: "I'll schedule a meeting for marketing strategy. What's the best time for everyone?", thread: {} },

    // Orphaned messages - Group 2 (HR/Onboarding)
    { name: "msg/11", sender: { displayName: "Ivan", name: "users/ivan" }, createTime: "2025-08-01T12:30:00.000Z", text: "Welcome to the team, new hires! Please complete your onboarding paperwork by Friday.", thread: {} },
    { name: "msg/12", sender: { displayName: "Judy", name: "users/judy" }, createTime: "2025-08-01T12:35:00.000Z", text: "Looking forward to it! Where can I find the benefits information?", thread: {} },

    // Orphaned messages - Group 3 (Company Events)
    { name: "msg/13", sender: { displayName: "Karl", name: "users/karl" }, createTime: "2025-08-01T13:00:00.000Z", text: "Reminder: Company picnic next Saturday at Central Park. RSVP by end of day.", thread: {} },
    { name: "msg/14", sender: { displayName: "Liam", name: "users/liam" }, createTime: "2025-08-01T13:05:00.000Z", text: "Sounds great! Will there be vegetarian options?", thread: {} },
  ];

  Log.info(`Created ${mockMessages.length} mock messages for testing the complete pipeline.`);

  // First, test the clustering function directly to isolate any issues
  Log.info("--- Testing clusterLargeDataset directly ---");
  const messagesForClustering = mockMessages.map(msg => ({ id: msg.name, text: msg.text }));
  Log.info(`Prepared ${messagesForClustering.length} messages for clustering:`);
  messagesForClustering.forEach((msg, index) => {
    Log.info(`  ${index + 1}. ${msg.id}: "${msg.text.substring(0, 50)}..."`);
  });

  let clusteringResult;
  try {
    Log.info("Calling clusterLargeDataset...");
    clusteringResult = clusterLargeDataset(messagesForClustering);
    Log.info(`Direct clustering result: ${clusteringResult ? clusteringResult.length : 'null'} clusters`);

    if (clusteringResult && clusteringResult.length > 0) {
      clusteringResult.forEach((cluster, index) => {
        Log.info(`  Cluster ${index + 1}: "${cluster.cluster_name}" (${cluster.message_ids.length} messages)`);
        Log.info(`    Message IDs: ${cluster.message_ids.join(', ')}`);
      });
    } else {
      Log.error("Direct clustering failed - no clusters returned");
      Log.error(`clusteringResult type: ${typeof clusteringResult}`);
      Log.error(`clusteringResult value: ${JSON.stringify(clusteringResult)}`);
    }
  } catch (e) {
    Log.error(`Direct clustering failed with error: ${e.message}`, { stack: e.stack });
  }

  let result;
  try {
    Log.info("Calling test-specific processing function with mock messages...");
    // Use the test-specific function that bypasses the "already processed" filter
    result = processChatHistoryAndStoreForTest_(mockMessages, TEST_FIRESTORE_COLLECTION_NAME, TEST_CLUSTERED_COLLECTION_NAME);
    Log.info("\n--- Full Process Result ---", result);

    // Additional debugging
    if (result) {
      Log.info(`Result structure: clusteredSummaries=${result.clusteredSummaries ? result.clusteredSummaries.length : 'undefined'}, saveReport=${result.saveReport ? 'present' : 'undefined'}`);
      if (result.clusteredSummaries && result.clusteredSummaries.length === 0) {
        Log.warn("No clustered summaries were generated. This could be due to:");
        Log.warn("1. Clustering API failed or returned no results");
        Log.warn("2. Gemini API issues");
        Log.warn("3. Invalid message format");
      }
    }
  } catch (e) {
    Log.error(`FAIL: processChatHistoryAndStoreForTest_ threw an error: ${e.message}`, { stack: e.stack });
    return { success: false, error: e.message };
  }

  // Comprehensive assertions to verify the output structure and content
  let testsPassed = 0;
  let totalTests = 0;

  // Test 1: Result structure validation
  totalTests++;
  if (result && typeof result === 'object' && result.clusteredSummaries && result.saveReport) {
    Log.info("PASS: Result has correct structure (clusteredSummaries and saveReport).");
    testsPassed++;
  } else {
    Log.error("FAIL: Result structure is invalid or missing required properties.");
  }

  // Test 2: Clustered summaries validation
  totalTests++;
  if (result && result.clusteredSummaries && result.clusteredSummaries.length > 0) {
    Log.info(`PASS: Clustered summaries generated. Count: ${result.clusteredSummaries.length}`);
    testsPassed++;

    // Detailed validation of each summary
    result.clusteredSummaries.forEach((summary, index) => {
      Log.info(`Cluster ${index + 1}: "${summary.cluster_name}" (Messages: ${summary.message_count})`);
      Log.info(`  Summary: ${summary.summary.substring(0, 150)}...`);
      if (summary.message_ids && summary.message_ids.length > 0) {
        Log.info(`  Sample Message ID: ${summary.message_ids[0]}`);
      }

      // Validate summary structure
      if (!summary.cluster_id || !summary.cluster_name || !summary.summary || !summary.message_ids || !summary.summarizedAt) {
        Log.warn(`Cluster ${index + 1} is missing required fields.`);
      }
    });
  } else {
    Log.error("FAIL: No clustered summaries generated or result is malformed.");
  }

  // Test 3: Firestore save validation
  totalTests++;
  if (result && result.saveReport && result.saveReport.successful > 0) {
    Log.info(`PASS: Successfully saved ${result.saveReport.successful} clustered summaries to Firestore.`);
    testsPassed++;
  } else {
    Log.error("FAIL: No clustered summaries saved to Firestore or save report is missing.");
    if (result && result.saveReport && result.saveReport.errors && result.saveReport.errors.length > 0) {
      Log.error("Save errors:", result.saveReport.errors);
    }
  }

  // Test 4: Message processing validation
  totalTests++;
  if (result && result.clusteredSummaries) {
    const totalProcessedMessages = result.clusteredSummaries.reduce((sum, cluster) => sum + (cluster.message_ids ? cluster.message_ids.length : 0), 0);
    if (totalProcessedMessages === mockMessages.length) {
      Log.info(`PASS: All ${mockMessages.length} mock messages were processed and assigned to clusters.`);
      testsPassed++;
    } else {
      Log.error(`FAIL: Message count mismatch. Expected ${mockMessages.length}, processed ${totalProcessedMessages}.`);
    }
  }

  // Final test summary
  const successRate = (testsPassed / totalTests * 100).toFixed(1);
  Log.info(`\n--- Full Process Test Complete ---`);
  Log.info(`Test Results: ${testsPassed}/${totalTests} tests passed (${successRate}%)`);

  if (testsPassed === totalTests) {
    Log.info("🎉 ALL TESTS PASSED! The full chat processing pipeline is working correctly.");
  } else {
    Log.warn(`⚠️  ${totalTests - testsPassed} test(s) failed. Please review the errors above.`);
  }

  return {
    success: testsPassed === totalTests,
    testsPassed: testsPassed,
    totalTests: totalTests,
    successRate: successRate,
    result: result
  };
}

/**
 * Test function to verify API key and basic Gemini connectivity.
 */
function testGeminiApiKey() {
  Log.info("--- Testing Gemini API Key ---");

  // Check if API key is loaded
  Log.info(`API Key loaded: ${GEMINI_API_KEY ? 'Yes' : 'No'}`);
  Log.info(`API Key length: ${GEMINI_API_KEY ? GEMINI_API_KEY.length : 'N/A'}`);
  Log.info(`API Key starts with: ${GEMINI_API_KEY ? GEMINI_API_KEY.substring(0, 10) + '...' : 'N/A'}`);

  // Test a simple API call
  const testUrl = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;
  const testPayload = {
    contents: [{
      parts: [{
        text: "Hello, just testing the API connection. Please respond with 'API working'."
      }]
    }]
  };

  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    payload: JSON.stringify(testPayload),
    muteHttpExceptions: true
  };

  try {
    Log.info("Making test API call to Gemini...");
    const response = UrlFetchApp.fetch(testUrl, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    Log.info(`API Response Code: ${responseCode}`);
    Log.info(`API Response: ${responseText.substring(0, 200)}...`);

    if (responseCode === 200) {
      Log.info("✅ API key is working correctly!");
      return { success: true };
    } else {
      Log.error(`❌ API call failed with code ${responseCode}`);
      return { success: false, error: `HTTP ${responseCode}: ${responseText}` };
    }
  } catch (e) {
    Log.error(`❌ API test failed: ${e.message}`, { stack: e.stack });
    return { success: false, error: e.message };
  }
}

/**
 * Test function to specifically test the getGeminiSummaryForCluster_ function
 * that was causing the 404 error.
 */
function testClusterSummarization() {
  Log.info("--- Testing Cluster Summarization Function ---");

  const testConversationText = `Alice (2025-08-01T10:00:00.000Z): Hello team! We need to discuss the new project timeline and resource allocation. Please come prepared with your estimates.
Bob (2025-08-01T10:01:00.000Z): Hi Alice! I have my estimates ready. For the frontend work, I'm thinking about 3 weeks.
Charlie (2025-08-01T10:02:00.000Z): Backend should take around 2 weeks if we use the existing API framework.
Alice (2025-08-01T10:03:00.000Z): Great! Let's schedule a detailed planning meeting for next week.`;

  const testClusterName = "Project Planning & Development";

  try {
    Log.info(`Testing summarization for cluster: "${testClusterName}"`);
    Log.info(`Conversation text length: ${testConversationText.length} characters`);

    const summary = getGeminiSummaryForCluster_(testConversationText, testClusterName);

    if (summary && summary.length > 0) {
      Log.info("✅ Cluster summarization successful!");
      Log.info(`Generated summary: ${summary}`);
      return { success: true, summary: summary };
    } else {
      Log.error("❌ Cluster summarization returned empty result");
      return { success: false, error: "Empty summary returned" };
    }
  } catch (e) {
    Log.error(`❌ Cluster summarization failed: ${e.message}`, { stack: e.stack });
    return { success: false, error: e.message };
  }
}

/**
 * Test function to generate and log test questions based on clustered summaries.
 * This function first runs the full chat processing pipeline to get clustered summaries,
 * then uses the new GeminiService function to generate test questions from them.
 * The generated questions are logged for review.
 * @public
 */
function testGenerateAndLogQuestions() {
  Log.info("--- Starting Test: Generate and Log Questions ---");

  // 1. Retrieve clustered summaries from the production collection
  Log.info("Retrieving clustered summaries from production collection...");
  let clusteredSummaries;
  try {
    clusteredSummaries = getCollectionDocuments(CLUSTERED_COLLECTION_NAME);
  } catch (e) {
    Log.error(`FAIL: Error retrieving production clustered summaries: ${e.message}`, { stack: e.stack });
    return { success: false, error: `Error retrieving production clustered summaries: ${e.message}` };
  }

  if (!clusteredSummaries || clusteredSummaries.length === 0) {
    Log.warn("No clustered summaries found in the production collection. Cannot proceed with question generation test.");
    return { success: false, error: "No production clustered summaries found." };
  }

  // The documents retrieved from Firestore will have a 'fields' property containing the actual data.
  // We need to transform them into the expected format for generateTestQuestionsFromClusters.
  const formattedSummaries = clusteredSummaries.map(doc => ({
    cluster_id: doc.id, // Using doc.id as cluster_id for existing docs
    cluster_name: doc.fields.cluster_name ? doc.fields.cluster_name.stringValue : 'Unknown Topic',
    summary: doc.fields.summary ? doc.fields.summary.stringValue : 'No summary available',
    message_ids: doc.fields.message_ids ? doc.fields.message_ids.arrayValue.values.map(v => v.stringValue) : [],
    message_count: doc.fields.message_count ? parseInt(doc.fields.message_count.integerValue) : 0,
    summarizedAt: doc.fields.summarizedAt ? doc.fields.summarizedAt.stringValue : ''
  }));

  Log.info(`Successfully obtained ${formattedSummaries.length} clustered summaries from production.`);

  // 2. Generate test questions from the formatted clustered summaries
  Log.info("Generating test questions from formatted clustered summaries using GeminiService...");
  let generatedQuestions;
  try {
    generatedQuestions = generateTestQuestionsFromClusters(formattedSummaries);
  } catch (e) {
    Log.error(`FAIL: Error generating test questions: ${e.message}`, { stack: e.stack });
    return { success: false, error: `Error generating test questions: ${e.message}` };
  }

  if (!generatedQuestions || generatedQuestions.length === 0) {
    Log.warn("No test questions were generated.");
    return { success: false, error: "No test questions generated." };
  }

  Log.info(`Successfully generated ${generatedQuestions.length} test questions.`);
  Log.info("\n--- Generated Test Questions ---");
  generatedQuestions.forEach((q, index) => {
    Log.info(`Question ${index + 1}: ${q.question}`);
    Log.info(`  Answer Available: ${q.answer_available}`);
    Log.info(`  Expected Answer: ${q.expected_answer}`);
    Log.info("---");
  });
  Log.info("--- End Generated Test Questions ---");

  Log.info("--- Test: Generate and Log Questions Complete ---");
  return { success: true, totalQuestions: generatedQuestions.length, questions: generatedQuestions };
}

/**
 * Test the clusterSingleChunk_ function directly with minimal data
 */
function testSingleChunkClustering() {
  Log.info("--- Testing Single Chunk Clustering ---");

  // Very simple test messages
  const testChunk = [
    { id: "msg1", text: "Let's discuss the project timeline." },
    { id: "msg2", text: "Server is down, investigating now." }
  ];

  Log.info(`Testing single chunk clustering with ${testChunk.length} messages...`);

  try {
    // Call the internal clustering function directly
    const result = clusterSingleChunk_(testChunk);

    Log.info(`Single chunk result:`, result);

    if (result && result.clusters && result.clusters.length > 0) {
      Log.info(`SUCCESS: Generated ${result.clusters.length} clusters from single chunk`);
      result.clusters.forEach((cluster, index) => {
        Log.info(`Cluster ${index + 1}: "${cluster.cluster_name}"`);
        Log.info(`  Messages: ${cluster.message_ids.join(', ')}`);
      });
      return { success: true, clusters: result.clusters.length };
    } else {
      Log.error("FAIL: No clusters generated from single chunk");
      Log.error(`Result structure: ${JSON.stringify(result)}`);
      return { success: false, error: "No clusters generated" };
    }
  } catch (e) {
    Log.error(`FAIL: Single chunk clustering threw error: ${e.message}`, { stack: e.stack });
    return { success: false, error: e.message };
  }
}

/**
 * Simple test function to debug clustering issues.
 * Tests just the clustering functionality with a small set of messages.
 */
function testClusteringOnly() {
  Log.info("--- Testing Clustering Only ---");

  // Simple test messages
  const testMessages = [
    { id: "msg1", text: "Let's discuss the project timeline and budget allocation for Q4." },
    { id: "msg2", text: "I think we need more resources for the backend development." },
    { id: "msg3", text: "The frontend is looking good but needs mobile optimization." },
    { id: "msg4", text: "Server is down! We need to investigate the database connection issue." },
    { id: "msg5", text: "Customer support is getting complaints about the outage." }
  ];

  Log.info(`Testing clustering with ${testMessages.length} simple messages...`);

  try {
    const result = clusterLargeDataset(testMessages);

    if (result && result.length > 0) {
      Log.info(`SUCCESS: Generated ${result.length} clusters`);
      result.forEach((cluster, index) => {
        Log.info(`Cluster ${index + 1}: "${cluster.cluster_name}"`);
        Log.info(`  Messages: ${cluster.message_ids.join(', ')}`);
      });
      return { success: true, clusters: result.length };
    } else {
      Log.error("FAIL: No clusters generated");
      Log.error(`Result type: ${typeof result}, value: ${JSON.stringify(result)}`);
      return { success: false, error: "No clusters generated" };
    }
  } catch (e) {
    Log.error(`FAIL: Clustering threw error: ${e.message}`, { stack: e.stack });
    return { success: false, error: e.message };
  }
}

/**
 * Function to run the chat summarizer with a sample set of messages.
 * This function can be executed directly from the Apps Script editor or via `clasp run`.
 */
function runWithSampleData() {
  Log.info("--- Starting runWithSampleData ---");
  // This function now calls the main processChatSpace function, which handles fetching
  // messages from the configured SPACE_NAME and storing them.
  // For testing with sample data, you might temporarily change SPACE_NAME in Config.js
  // or create a dedicated test space and update TEST_SPACE_ID.
  const result = processChatSpace(); // Calls the main processing function
  Log.info("--- runWithSampleData Complete ---", result);
  return result;
}

/**
 * Unit test for `GeminiService.clusterLargeDataset`.
 * Mocks the internal API calls to verify the Map-Reduce orchestration.
 */
function testClusterLargeDataset() {
  Log.info("--- Starting clusterLargeDataset Unit Test ---");

  const mockAllMessages = [
    { id: "msg1", text: "Meeting about Q4 planning and budget allocation." },
    { id: "msg2", text: "Discussion on new marketing campaign strategies." },
    { id: "msg3", text: "Follow-up on last week's sales figures." },
    { id: "msg4", text: "Team building event ideas for next month." },
    { id: "msg5", text: "Feedback on the new product feature rollout." },
    { id: "msg6", text: "Bug report: Login flow is broken on mobile." },
    { id: "msg7", text: "Request for vacation approval process." },
    { id: "msg8", text: "Update on server migration to new data center." },
    { id: "msg9", text: "Question about expense report submission." },
    { id: "msg10", text: "Brainstorming session for next sprint's features." },
    { id: "msg11", text: "Discussion on Q4 financial projections." },
    { id: "msg12", text: "Review of marketing materials for product launch." },
    { id: "msg13", text: "Planning for the annual company retreat." },
    { id: "msg14", text: "User feedback on performance issues." },
    { id: "msg15", text: "Issue with database connection in production." },
  ];

  // Mock the internal helper functions of GeminiService
  const originalClusterSingleChunk = typeof _clusterSingleChunk !== 'undefined' ? _clusterSingleChunk : null;
  const originalConsolidateClusterTopics = typeof _consolidateClusterTopics !== 'undefined' ? _consolidateClusterTopics : null;
  const originalChunkArray = typeof _chunkArray !== 'undefined' ? _chunkArray : null;

  // Mock _chunkArray to control chunking behavior for the test
  _chunkArray = (arr, size) => {
    Log.debug("Mocking _chunkArray. Array length:", arr.length, "Chunk size:", size);
    // Simulate 3 chunks for simplicity
    return [
      arr.slice(0, 5),
      arr.slice(5, 10),
      arr.slice(10, 15)
    ];
  };

  // Mock _clusterSingleChunk to return predictable local clusters
  _clusterSingleChunk = (chunk) => {
    Log.debug("Mocking _clusterSingleChunk for chunk:", chunk.map(m => m.id));
    if (chunk.some(m => m.id === "msg1" || m.id === "msg11")) {
      return { clusters: [{ cluster_id: 1, cluster_name: "Financial Planning", message_ids: chunk.filter(m => m.id === "msg1" || m.id === "msg11").map(m => m.id) }] };
    }
    if (chunk.some(m => m.id === "msg2" || m.id === "msg12")) {
      return { clusters: [{ cluster_id: 2, cluster_name: "Marketing Campaigns", message_ids: chunk.filter(m => m.id === "msg2" || m.id === "msg12").map(m => m.id) }] };
    }
    if (chunk.some(m => m.id === "msg3")) {
      return { clusters: [{ cluster_id: 3, cluster_name: "Sales Performance", message_ids: chunk.filter(m => m.id === "msg3").map(m => m.id) }] };
    }
    if (chunk.some(m => m.id === "msg4" || m.id === "msg13")) {
      return { clusters: [{ cluster_id: 4, cluster_name: "Team Events", message_ids: chunk.filter(m => m.id === "msg4" || m.id === "msg13").map(m => m.id) }] };
    }
    if (chunk.some(m => m.id === "msg5" || m.id === "msg10")) {
      return { clusters: [{ cluster_id: 5, cluster_name: "Product Features", message_ids: chunk.filter(m => m.id === "msg5" || m.id === "msg10").map(m => m.id) }] };
    }
    if (chunk.some(m => m.id === "msg6" || m.id === "msg14" || m.id === "msg15")) {
      return { clusters: [{ cluster_id: 6, cluster_name: "Technical Issues", message_ids: chunk.filter(m => m.id === "msg6" || m.id === "msg14" || m.id === "msg15").map(m => m.id) }] };
    }
    if (chunk.some(m => m.id === "msg7" || m.id === "msg9")) {
      return { clusters: [{ cluster_id: 7, cluster_name: "HR & Admin", message_ids: chunk.filter(m => m.id === "msg7" || m.id === "msg9").map(m => m.id) }] };
    }
    if (chunk.some(m => m.id === "msg8")) {
      return { clusters: [{ cluster_id: 8, cluster_name: "Infrastructure Updates", message_ids: chunk.filter(m => m.id === "msg8").map(m => m.id) }] };
    }
    return { clusters: [] };
  };

  // Mock _consolidateClusterTopics to return a predictable topic map
  _consolidateClusterTopics = (topicNames) => {
    Log.debug("Mocking _consolidateClusterTopics for topicNames:", topicNames);
    const map = {};
    topicNames.forEach(name => {
      if (name.includes("Planning") || name.includes("Financial")) map[name] = "Financial & Strategic Planning";
      else if (name.includes("Marketing") || name.includes("Campaigns")) map[name] = "Marketing & Sales";
      else if (name.includes("Sales")) map[name] = "Marketing & Sales";
      else if (name.includes("Team") || name.includes("Events") || name.includes("Retreat")) map[name] = "Team & Culture";
      else if (name.includes("Product") || name.includes("Features") || name.includes("Sprint")) map[name] = "Product Development";
      else if (name.includes("Technical") || name.includes("Bug") || name.includes("Performance") || name.includes("Database") || name.includes("Production")) map[name] = "Technical Support & Issues";
      else if (name.includes("HR") || name.includes("Admin") || name.includes("Vacation") || name.includes("Expense")) map[name] = "Human Resources & Admin";
      else if (name.includes("Infrastructure") || name.includes("Server")) map[name] = "IT Infrastructure";
      else map[name] = name; // Default to original if no consolidation
    });
    return map;
  };

  try {
    const result = clusterLargeDataset(mockAllMessages, 5); // Use a small chunk size for testing
    Log.info("clusterLargeDataset Test Result:", result);

    // Assertions
    if (result && result.length > 0) {
      Log.info("PASS: clusterLargeDataset returned clusters.");
      // Verify some expected consolidated topics and message counts
      const financialCluster = result.find(c => c.cluster_name === "Financial & Strategic Planning");
      if (financialCluster && financialCluster.message_ids.includes("msg1") && financialCluster.message_ids.includes("msg11")) {
        Log.info("PASS: Financial & Strategic Planning cluster found with correct messages.");
      } else {
        Log.error("FAIL: Financial & Strategic Planning cluster missing or incorrect.");
      }

      const techIssuesCluster = result.find(c => c.cluster_name === "Technical Support & Issues");
      if (techIssuesCluster && techIssuesCluster.message_ids.includes("msg6") && techIssuesCluster.message_ids.includes("msg14") && techIssuesCluster.message_ids.includes("msg15")) {
        Log.info("PASS: Technical Support & Issues cluster found with correct messages.");
      } else {
        Log.error("FAIL: Technical Support & Issues cluster missing or incorrect.");
      }

      // Verify total message IDs match original count
      const totalMessageIdsInClusters = result.reduce((acc, curr) => acc + curr.message_ids.length, 0);
      if (totalMessageIdsInClusters === mockAllMessages.length) {
        Log.info("PASS: All original messages are accounted for in the final clusters.");
      } else {
        Log.error(`FAIL: Message ID count mismatch. Expected ${mockAllMessages.length}, got ${totalMessageIdsInClusters}.`);
      }

    } else {
      Log.error("FAIL: clusterLargeDataset returned no clusters or an invalid result.");
    }

  } catch (e) {
    Log.error(`Test failed with an unexpected error: ${e.message}`, { stack: e.stack });
  } finally {
    // Restore original functions
    if (originalClusterSingleChunk) _clusterSingleChunk = originalClusterSingleChunk;
    if (originalConsolidateClusterTopics) _consolidateClusterTopics = originalConsolidateClusterTopics;
    if (originalChunkArray) _chunkArray = originalChunkArray;
    Log.info("--- clusterLargeDataset Unit Test Complete ---");
  }
}

/**
 * Proof-of-concept test function for `GeminiService.answerQuestionFromCollection`.
 * This function demonstrates how to use the question-answering API with a sample question.
 *
 * IMPORTANT: For this test to return a meaningful answer, the `TEST_FIRESTORE_COLLECTION_NAME`
 * (e.g., 'chat_summaries' or a dedicated test collection) must contain documents
 * with content relevant to the question. If the collection is empty or irrelevant,
 * the function will likely return "NOT_FOUND".
 *
 * @public
 */
function testAnswerQuestionFromCollection() {
  Log.info("--- Starting testAnswerQuestionFromCollection Proof of Concept ---");

  const collectionToSearch = 'clustered-chat-summaries'; // Or another relevant collection
  // const question = "What's the status of accessing Siloed code? I think i remember seeing a bug where it was approved";
  const question = "can LLM extension read my Google slides?";
  try {
    // Call the new question-answering function
    const geminiResponse = answerQuestionFromCollection(collectionToSearch, question);

    Log.info(`Question: "${question}"`);
    Log.info(`Gemini's Response: "${geminiResponse}"`);

    // You can add assertions here based on expected outcomes if you have mock data
    if (geminiResponse === "NOT_A_QUESTION") {
      Log.warn("Test Result: Input was identified as NOT_A_QUESTION.");
    } else if (geminiResponse === "NOT_FOUND") {
      Log.warn("Test Result: Answer NOT_FOUND in the collection context.");
    } else if (geminiResponse === null) {
      Log.error("Test Result: An error occurred during the API call or processing.");
    } else {
      Log.info("Test Result: Answer provided by Gemini.");
    }
    return `Gemini's Response: "${geminiResponse}"`;
  } catch (e) {
    Log.error(`testAnswerQuestionFromCollection failed with an unexpected error: ${e.message}`, { stack: e.stack });
  } finally {
    Log.info("--- testAnswerQuestionFromCollection Proof of Concept Complete ---");
  }
}

/**
 * Unit test for `_clusterSingleChunk`.
 * Mocks the `UrlFetchApp.fetch` to simulate Gemini API response for a single chunk.
 */
function test_clusterSingleChunk() {
  Log.info("--- Starting _clusterSingleChunk Unit Test ---");

  const mockMessageChunk = [
    { id: "m1", text: "User: My bill is incorrect. Can you help?" },
    { id: "m2", text: "Agent: Please provide your account number for billing inquiry." },
    { id: "m3", text: "User: I need to reset my password. It's not working." }
  ];

  const originalUrlFetchApp = UrlFetchApp;
  UrlFetchApp = {
    fetch: function (url, options) {
      Log.debug("Mocking UrlFetchApp.fetch for _clusterSingleChunk.", { url: url, options: options });
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                clusters: [
                  { cluster_id: 1, cluster_name: "Billing Issues", message_ids: ["m1", "m2"] },
                  { cluster_id: 2, cluster_name: "Account Access", message_ids: ["m3"] }
                ]
              })
            }]
          }
        }]
      };
      return {
        getResponseCode: () => 200,
        getContentText: () => JSON.stringify(mockResponse)
      };
    }
  };

  try {
    const result = _clusterSingleChunk(mockMessageChunk);
    Log.info("_clusterSingleChunk Test Result (Success):", result);

    if (result && result.clusters && result.clusters.length === 2) {
      Log.info("PASS: _clusterSingleChunk returned expected number of clusters.");
      if (result.clusters[0].cluster_name === "Billing Issues" && result.clusters[1].cluster_name === "Account Access") {
        Log.info("PASS: Cluster names are correct.");
      } else {
        Log.error("FAIL: Cluster names are incorrect.");
      }
      if (result.clusters[0].message_ids.includes("m1") && result.clusters[0].message_ids.includes("m2") && result.clusters[1].message_ids.includes("m3")) {
        Log.info("PASS: Message IDs are correctly assigned.");
      } else {
        Log.error("FAIL: Message IDs are incorrectly assigned.");
      }
    } else {
      Log.error("FAIL: _clusterSingleChunk returned unexpected result:", result);
    }

    // Test error case
    Log.info("--- Starting _clusterSingleChunk Error Test ---");
    UrlFetchApp.fetch = function (url, options) {
      Log.debug("Mocking UrlFetchApp.fetch for _clusterSingleChunk error scenario.");
      return {
        getResponseCode: () => 500,
        getContentText: () => JSON.stringify({ error: { message: "API error" } })
      };
    };

    const errorResult = _clusterSingleChunk(mockMessageChunk);
    Log.info("_clusterSingleChunk Test Result (Error):", errorResult);

    if (errorResult === null) {
      Log.info("PASS: _clusterSingleChunk returned null on API error.");
    } else {
      Log.error("FAIL: _clusterSingleChunk did not return null on API error.");
    }

  } catch (e) {
    Log.error(`Test failed with an unexpected error: ${e.message}`, { stack: e.stack });
  } finally {
    UrlFetchApp = originalUrlFetchApp;
    Log.info("--- _clusterSingleChunk Unit Test Complete ---");
  }
}

/**
 * Unit test for `_consolidateClusterTopics`.
 * Mocks the `UrlFetchApp.fetch` to simulate Gemini API response for topic consolidation.
 */
function test_consolidateClusterTopics() {
  Log.info("--- Starting _consolidateClusterTopics Unit Test ---");

  const mockTopicNames = ["Billing Inquiries", "Payment Issues", "Feature Requests", "New Features", "Technical Support"];

  const originalUrlFetchApp = UrlFetchApp;
  UrlFetchApp = {
    fetch: function (url, options) {
      Log.debug("Mocking UrlFetchApp.fetch for _consolidateClusterTopics.", { url: url, options: options });
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify({
                topic_map: [
                  { original_topic: "Billing Inquiries", consolidated_topic: "Billing & Payments" },
                  { original_topic: "Payment Issues", consolidated_topic: "Billing & Payments" },
                  { original_topic: "Feature Requests", consolidated_topic: "Product Features" },
                  { original_topic: "New Features", consolidated_topic: "Product Features" },
                  { original_topic: "Technical Support", consolidated_topic: "Technical Support" }
                ]
              })
            }]
          }
        }]
      };
      return {
        getResponseCode: () => 200,
        getContentText: () => JSON.stringify(mockResponse)
      };
    }
  };

  try {
    const result = _consolidateClusterTopics(mockTopicNames);
    Log.info("_consolidateClusterTopics Test Result (Success):", result);

    if (result && result["Billing Inquiries"] === "Billing & Payments" &&
      result["Payment Issues"] === "Billing & Payments" &&
      result["Feature Requests"] === "Product Features" &&
      result["New Features"] === "Product Features" &&
      result["Technical Support"] === "Technical Support") {
      Log.info("PASS: _consolidateClusterTopics returned correct topic map.");
    } else {
      Log.error("FAIL: _consolidateClusterTopics returned incorrect topic map:", result);
    }

    // Test error case
    Log.info("--- Starting _consolidateClusterTopics Error Test ---");
    UrlFetchApp.fetch = function (url, options) {
      Log.debug("Mocking UrlFetchApp.fetch for _consolidateClusterTopics error scenario.");
      return {
        getResponseCode: () => 500,
        getContentText: () => JSON.stringify({ error: { message: "API error" } })
      };
    };

    const errorResult = _consolidateClusterTopics(mockTopicNames);
    Log.info("_consolidateClusterTopics Test Result (Error):", errorResult);

    if (errorResult === null) {
      Log.info("PASS: _consolidateClusterTopics returned null on API error.");
    } else {
      Log.error("FAIL: _consolidateClusterTopics did not return null on API error.");
    }

  } catch (e) {
    Log.error(`Test failed with an unexpected error: ${e.message}`, { stack: e.stack });
  } finally {
    UrlFetchApp = originalUrlFetchApp;
    Log.info("--- _consolidateClusterTopics Unit Test Complete ---");
  }
}
