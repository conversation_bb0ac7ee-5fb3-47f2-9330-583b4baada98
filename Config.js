const MAX_GAP_MINUTES = 15;
const FIRESTORE_COLLECTION_NAME = 'chat-chunks'; // For raw messages, if still needed
const CLUSTERED_COLLECTION_NAME = 'clustered-chat-summaries'; // New collection for clustered summaries
const FIRESTORE_BATCH_SIZE = 500; // For efficient batch writes

// Test-specific Firestore collection names
const TEST_FIRESTORE_COLLECTION_NAME = 'test-chat-chunks';
const TEST_CLUSTERED_COLLECTION_NAME = 'test-clustered-chat-summaries';
const METADATA_COLLECTION_NAME = 'processing_metadata'; // Collection for storing last processed timestamps

const SPACE_NAME = 'spaces/AAQAlEm3jF8'; // LLM extension space
const GEMINI_API_KEY = 'AIzaSyAbLkAK41p8jeOPN7uCm4I_rXDJuw97Z7o'; // Ensure this is correct
const GEMINI_MODEL = 'gemini-2.5-flash'; // Model name without the :generateContent suffix (added in URL construction)
const LOOKBACK_DAYS = 15; // Default number of days to go back for initial chat history fetch

// Firestore configuration
const FIRESTORE_PROJECT_ID = 'rzager-chatbot'; // Google Cloud Project ID for Firestore

// Gemini API token limits for summarization
const MAX_SUMMARY_INPUT_TOKENS = 500000; // Max input tokens for a single summarization call (conservative, model limit is 1M)
const MAX_SUMMARY_OUTPUT_TOKENS = 8192; // Max output tokens for a summary (model limit is 65k, but we want concise)

// Chunking for clustering
const MAX_MESSAGES_PER_CHUNK = 100; // Max number of messages to send in a single clustering API call
