/**
 * @fileoverview DocExportService provides functions to export clustered chat summaries
 * from Firestore to a new Google Document.
 */

/**
 * Exports clustered chat summaries for a given space to a new Google Document.
 * The document will be titled "Chat Space Summary Export - [Space Name] - [Timestamp]".
 *
 * @param {string} spaceId The ID of the chat space to export summaries for.
 * @returns {string} The URL of the newly created Google Document.
 * @throws {Error} If the export fails at any stage (e.g., Firestore access, Doc creation).
 */
function exportClusteredSummariesToDoc(spaceId) {
  Log.info(`Starting export of clustered summaries for space: ${spaceId} to Google Doc.`);

  const projectId = FIRESTORE_PROJECT_ID;

  if (!projectId) {
    const errorMessage = "Firestore project ID not found in Config.js. Please set 'FIRESTORE_PROJECT_ID'.";
    Log.critical(errorMessage);
    throw new Error(errorMessage);
  }

  // Service account email must have 'Cloud Datastore User' or 'Cloud Datastore Owner' role
  const serviceAccountEmail = '<EMAIL>';
  const firestoreScopes = ['https://www.googleapis.com/auth/datastore'];

  let accessToken;
  try {
    accessToken = getServiceAccountToken(serviceAccountEmail, firestoreScopes);
  } catch (e) {
    Log.error("Failed to get service account token for Firestore access:", { message: e.message });
    throw new Error(`Failed to get service account token for Firestore access: ${e.message}`);
  }

  const firestore = FirestoreApp.getFirestore(accessToken, projectId);
  let clusteredSummaries = [];

  try {
    // Fetch all document names (paths) from the clustered summaries collection
    const documentNames = listFirestoreCollectionDocuments(projectId, CLUSTERED_COLLECTION_NAME, accessToken);
    Log.info(`Found ${documentNames.length} clustered summary documents in Firestore.`);

    // Fetch the content of each document
    for (const fullDocPath of documentNames) {
      // Extract the relative path (e.g., "collectionName/docId") from the full resource path
      const pathParts = fullDocPath.split('/');
      const relativeDocPath = `${pathParts[pathParts.length - 2]}/${pathParts[pathParts.length - 1]}`;

      const doc = firestore.getDocument(relativeDocPath);
      if (doc && doc.fields) {
        // Assuming the clustered summary documents have fields like 'clusterId', 'summary', 'messages'
        const summaryData = {};
        for (const key in doc.fields) {
          if (doc.fields.hasOwnProperty(key)) {
            const field = doc.fields[key];
            if (field.stringValue) {
              summaryData[key] = field.stringValue;
            } else if (field.arrayValue && field.arrayValue.values) {
              summaryData[key] = field.arrayValue.values.map(v => v.stringValue || v.integerValue || v.doubleValue || v.booleanValue);
            } else if (field.integerValue) {
              summaryData[key] = parseInt(field.integerValue);
            } else if (field.booleanValue) {
              summaryData[key] = field.booleanValue;
            }
            // Add more type handling as needed (e.g., mapValue, timestampValue)
          }
        }
        clusteredSummaries.push(summaryData);
      }
    }
    Log.info(`Successfully fetched ${clusteredSummaries.length} clustered summaries.`);
  } catch (e) {
    Log.error(`Error fetching clustered summaries from Firestore: ${e.message}`, { stack: e.stack });
    throw new Error(`Failed to fetch clustered summaries: ${e.message}`);
  }

  if (clusteredSummaries.length === 0) {
    Log.warn(`No clustered summaries found for space: ${spaceId}. No document will be created.`);
    return "No summaries found to export.";
  }

  // Sort summaries by clusterId if available, or by a relevant timestamp/order
  clusteredSummaries.sort((a, b) => {
    if (a.clusterId && b.clusterId) {
      return a.clusterId.localeCompare(b.clusterId);
    }
    // Fallback to other sorting if clusterId is not present or not sufficient
    return 0;
  });

  // Create a new Google Document
  let doc;
  const docTitle = `Chat Space Summary Export - ${spaceId} - ${new Date().toLocaleString()}`;
  try {
    doc = DocumentApp.create(docTitle);
    Log.info(`Created new Google Doc: "${docTitle}" (ID: ${doc.getId()})`);
  } catch (e) {
    Log.error(`Error creating Google Doc: ${e.message}`, { stack: e.stack });
    throw new Error(`Failed to create Google Doc: ${e.message}`);
  }

  const body = doc.getBody();
  body.appendParagraph(doc.getName()).setHeading(DocumentApp.ParagraphHeading.TITLE);
  body.appendParagraph(`Export Date: ${new Date().toLocaleString()}`).setHeading(DocumentApp.ParagraphHeading.SUBTITLE);
  body.appendParagraph(`Source Space ID: ${spaceId}`).setHeading(DocumentApp.ParagraphHeading.SUBTITLE);
  body.appendHorizontalRule();

  // Append each clustered summary to the document
  clusteredSummaries.forEach((summary, index) => {
    body.appendParagraph(`Cluster ${index + 1}: ${summary.clusterId || 'N/A'}`).setHeading(DocumentApp.ParagraphHeading.HEADING1);
    if (summary.summary) {
      body.appendParagraph('Summary:').setHeading(DocumentApp.ParagraphHeading.HEADING2);
      body.appendParagraph(summary.summary);
    }
    if (summary.messages && Array.isArray(summary.messages)) {
      body.appendParagraph('Key Messages:').setHeading(DocumentApp.ParagraphHeading.HEADING2);
      summary.messages.forEach(message => {
        body.appendListItem(message).setGlyphType(DocumentApp.GlyphType.BULLET);
      });
    }
    if (summary.keywords && Array.isArray(summary.keywords)) {
      body.appendParagraph('Keywords:').setHeading(DocumentApp.ParagraphHeading.HEADING2);
      body.appendParagraph(summary.keywords.join(', '));
    }
    body.appendHorizontalRule();
  });

  doc.saveAndClose();
  const docUrl = doc.getUrl();
  Log.info(`Successfully exported clustered summaries to Google Doc: ${docUrl}`);
  return docUrl;
}
