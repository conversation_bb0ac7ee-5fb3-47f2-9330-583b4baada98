/**
 * @fileoverview This service handles fetching chat messages from Google Chat spaces
 * and resolving user details.
 */

/**
 * Fetches all messages from a Chat space within a specified lookback period.
 * Fetches all messages from a Chat space within a specified lookback period.
 * It attempts to resolve sender display names from the message data itself.
 *
 * @param {string} spaceId The ID of the space (e.g., "spaces/AAAA...").
 * @param {number} lookbackDays The number of days to go back in history to fetch messages.
 * @param {string|null} [lastProcessedTimestamp=null] Optional: The timestamp of the last message processed.
 *   If provided, only messages newer than this timestamp will be fetched.
 * @returns {Array<Object>} An array of message objects with resolved sender display names.
 */
function getChatHistory(spaceId, lookbackDays, lastProcessedTimestamp = null) {
  Log.info(`Fetching chat history for space: ${spaceId}, lookbackDays: ${lookbackDays}, lastProcessedTimestamp: ${lastProcessedTimestamp}`);

  let allMessages = [];
  let nextPageToken = null;
  const pageSize = 500; // Max page size for Chat API

  const requestOptions = { pageSize: pageSize };

  // Determine the start time for fetching messages
  let fetchStartTime = null;
  if (lastProcessedTimestamp) {
    // If a last processed timestamp exists, fetch messages newer than that
    fetchStartTime = lastProcessedTimestamp;
    Log.info(`Fetching messages created after: ${fetchStartTime}`);
  } else {
    // Otherwise, go back 'lookbackDays' from now
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - lookbackDays);
    fetchStartTime = startDate.toISOString();
    Log.info(`No last processed timestamp found. Fetching messages created after: ${fetchStartTime} (based on lookbackDays)`);
  }

  // Apply the filter for message creation time
  requestOptions.filter = `createTime > "${fetchStartTime}"`;

  try {
    do {
      requestOptions.pageToken = nextPageToken;
      const response = Chat.Spaces.Messages.list(spaceId, requestOptions);

      if (response.messages && response.messages.length > 0) {
        // Resolve sender display names directly from message.sender or default
        const messagesWithResolvedSenders = response.messages.map(message => {
          message.sender = message.sender || {}; // Ensure sender object exists
          message.sender.displayName = message.sender.displayName || (message.sender.type === 'BOT' ? 'Bot' : 'Unknown User');
          return message;
        });
        allMessages = allMessages.concat(messagesWithResolvedSenders);
        Log.debug(`Fetched ${response.messages.length} messages. Total so far: ${allMessages.length}`);
      }
      nextPageToken = response.nextPageToken;

    } while (nextPageToken);

    Log.info(`Finished fetching messages for ${spaceId}. Total messages fetched: ${allMessages.length}`);

    // Sort messages chronologically before returning
    return allMessages.sort((a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime());

  } catch (e) {
    Log.error(`Error fetching chat history for ${spaceId}: ${e.message}`, { spaceId: spaceId, error: e.message, stack: e.stack });
    throw new Error(`Failed to retrieve messages from space ${spaceId}. Please check access and Space ID. Original error: ${e.message}`);
  }
}

/**
 * Retrieves a user's display name directly from the message sender object.
 * This function is a simplified version that does not use the People API.
 *
 * @private
 * @param {Object} sender The sender object from a Google Chat message.
 * @return {string} The user's display name or "Unknown User" / "App".
 */
function getUserDetails(sender) {
  if (!sender) {
    return { name: null, displayName: 'Unknown User', email: null };
  }
  if (sender.type === 'BOT') {
    return { name: sender.name, displayName: 'App', email: null };
  }
  return {
    name: sender.name,
    displayName: sender.displayName || 'Unknown User',
    email: sender.email || null, // email might not be available
  };
}

/**
 * Normalizes a space ID from a URL or direct ID.
 * @private
 * @param {string} input The raw space ID or URL.
 * @returns {string|null} The normalized space ID (e.g., "spaces/AAAA...") or null if invalid.
 */
function normalizeSpaceId(input) {
  if (!input || typeof input !== 'string') return null;
  const match = input.match(/\/room\/([a-zA-Z0-9_-]+)/);
  if (match && match[1]) return 'spaces/' + match[1];
  if (input.startsWith('spaces/')) return input;
  return null;
}
