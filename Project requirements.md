# **Project requirements**

# **Role**

You are an expert Google Apps Script developer specializing in data processing and integrations with Google Workspace and Google Cloud services. You write clean, efficient, and well-documented code using modern JavaScript (V8 runtime).

## **Objective**

Your task is to write Google Apps Script code that processes a history of Google Chat `Message` objects into a structured array of text chunks and then saves those chunks to a Google Cloud Firestore database.

The core logic involves a multi-pass strategy:

1.  Group messages by their explicit `thread.name`.
2.  Group remaining unthreaded messages into conversations using a time-based window.
3.  Break down these conversation groups into smaller, fixed-size chunks with overlap.
4.  Save the resulting chunks as individual documents in a Firestore collection.

## **Input Data**

The primary input will be an array of Google Chat `Message` objects. Here is a simplified structure of a single `Message` object for your reference:

```json
{
  "name": "spaces/AAAAAAAAAAA/messages/BBBBBBBBBBB.CCCCCCCCCC",
  "sender": {
    "name": "users/123456789",
    "displayName": "<PERSON> Doe",
    "type": "HUMAN"
  },
  "createTime": "2025-08-01T22:42:14.717Z",
  "text": "Thanks! That solved the issue.",
  "thread": {
    "name": "spaces/AAAAAAAAAAA/threads/DDDDDDDDDDD"
  }
}
```

*Note: Some messages may not have a `thread` property if they are not part of a thread.*

## **Detailed Requirements**

Please implement the solution following these steps precisely.

### **1. Function Definition & Configuration**

  * The main function signature should be `function chunkAndStoreChatHistory(messages)`.
  * Define configuration constants at the top of the script for easy modification:
      * `MAX_GAP_MINUTES = 15;` (The maximum time between messages to be considered part of the same unthreaded conversation).
      * `CHUNK_SIZE_CHARS = 1000;` (The target size for final text chunks, in characters).
      * `CHUNK_OVERLAP_CHARS = 150;` (The number of characters to overlap between consecutive chunks).
      * `FIRESTORE_COLLECTION_NAME = 'chat-chunks';` (The name of the Firestore collection to store chunks in).
      * `FIRESTORE_BATCH_SIZE = 500;` (The number of chunks to include in each Firestore batch write).
      * `SPACE_NAME = 'spaces/AAQAlEm3jF8';` (The Google Chat space name for specific operations, if needed).

### **2. First Pass: Group by Explicit Threads**

  * Iterate through the input `messages` array.
  * Use a JavaScript `Map` to group messages by their `message.thread.name`.
  * For each message, format it as a string: `[Sender Name] ([Timestamp]): [Message Text]`. Use ISO 8601 format for timestamps (e.g., `2025-08-01T22:42:14.717Z`).
  * Store messages that do not have a `thread.name` in a separate "orphans" array for the next step.

### **3. Second Pass: Group Orphans by Time Window**

  * Take the "orphans" array from the previous step and sort it chronologically by `createTime`.
  * Iterate through the sorted orphans. Group consecutive messages into a single conversation block as long as the time difference between them is less than `MAX_GAP_MINUTES`.
  * When the time gap is exceeded, finalize the current conversation block and start a new one.

### **4. Third Pass: Consolidate and Chunk**

  * You should now have a collection of large text blocks: one for each explicit thread and one for each time-based group of orphans.
  * Create a helper function, `splitTextIntoChunks(text, chunkSize, overlapSize)`, that takes a string and implements the fixed-size chunking logic.
      * This function should break the large text block into smaller chunks of approximately `chunkSize` characters.
      * Crucially, each subsequent chunk must start by repeating the last `overlapSize` characters of the previous chunk to maintain context across boundaries.
      * **Important:** This function must also handle cases where a single formatted message is longer than `chunkSize` by splitting it across multiple chunks to prevent truncation.
  * Apply this helper function to every large conversation block you generated in steps 2 and 3.

## **Integration: Storing Chunks in Firestore 🔥**

After generating the chunks, they must be saved to Firestore.

### **1. Setup and Authentication**

  * **Add Firestore Library:** Instruct the user via comments on how to add the `FirestoreApp` library for Apps Script. The library ID is `1VUSl4b1r1L51_C33_sXV_4h1gMzeO9U898ko_cM3ecI-4DPN2MyY22ol`.
  * **Service Account:** Include comments explaining that the user must create a Service Account in their Google Cloud Project.
    1.  In the GCP Console, go to "IAM & Admin" \> "Service Accounts".
    2.  Create a new service account.
    3.  Grant it the **"Cloud Datastore User"** role.
    4.  Create a JSON key for the service account and download it.
  * **Store Credentials:** Explain via comments that the user must copy the `private_key`, `client_email`, and `project_id` from the downloaded JSON key and store them securely in Apps Script's `PropertiesService`.

### **2. `saveChunksToFirestore` Function**

  * Create a new function: `function saveChunksToFirestore(chunks)`.
  * Inside this function, retrieve the credentials from `PropertiesService`.
  * Initialize the `FirestoreApp` library with these credentials.
  * Implement batch writes: Group the `chunks` array into batches of `FIRESTORE_BATCH_SIZE`.
  * For each batch, use `firestore.batch()` to create a batch, add `createDocument` operations for each chunk in the batch, and then `commit()` the batch.
  * Implement robust error handling: Wrap each batch commit in a `try...catch` block. If a batch fails, log the error and continue processing subsequent batches. The function should return a report detailing which chunks were successfully saved and which failed.

### **3. Update Main Function**

  * At the end of the `chunkAndStoreChatHistory` function, after the chunks have been generated, add a call to `saveChunksToFirestore(finalChunks)`.

## **Output Specification**

The primary `chunkAndStoreChatHistory` function, after saving the data to Firestore, should return a single flat array of the generated chunk objects. Each object represents a final chunk and must have the following structure:

```json
{
  "content": "Jane Doe (8/3/2025, 4:10 PM): Thanks! That solved the issue. John Smith (8/3/2025, 4:11 PM): No problem, glad I could help out. Let me know if you need anything else...",
  "metadata": {
    "source": "thread_DDDDDDDDDDD", // or "time_group_1" for unthreaded conversations
    "charCount": 987,
    "startTime": "2025-08-03T23:10:05.123Z",
    "endTime": "2025-08-03T23:11:30.456Z"
  }
}
```

## **Best Practices**

  * Add JSDoc comments to all functions explaining what they do, their parameters (`@param`), and what they return (`@return`).
  * Include clear setup instructions in the comments for the end-user, especially for the Firestore library and service account credentials.
  * Use modern JavaScript features like `const`, `let`, arrow functions, and spread syntax where appropriate.
  * Ensure the code is robust and handles edge cases, such as an empty input array or messages with no text.

Please provide the complete and runnable Google Apps Script code for this entire task.
