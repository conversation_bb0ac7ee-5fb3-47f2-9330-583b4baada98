// GCP Project ID destination for your logs
const GCP_PROJECT_ID = 'rzager-chatbot';

/**
 * Global Log instance for structured logging to Google Cloud.
 * Automatically configures itself on first use.
 * Requires the structured logging library (ID: 1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u)
 */
let Log;

/**
 * Initializes the Log instance with automatic configuration if needed.
 */
function initializeLogger() {
  try {
    Log = Logger.getInstance(PropertiesService.getScriptProperties());

    // Verify logger is properly configured
    if (!Log || !Log.getLogsUrl || typeof Log.info !== 'function') {
      throw new Error('Logger instance not properly configured');
    }

    console.log('Logger successfully initialized with existing configuration.');

  } catch (error) {
    console.log('Logger not configured. Running automatic configuration...');
    console.log('Error details:', error.message);

    try {
      Logger.setup(GCP_PROJECT_ID, PropertiesService.getScriptProperties());
      Log = Logger.getInstance(PropertiesService.getScriptProperties());
      console.log('Logger automatically configured and initialized successfully.');

    } catch (setupError) {
      console.error('Failed to automatically configure logger:', setupError.message);
      // Fallback to console logging
      Log = {
        debug: console.log,
        info: console.log,
        warn: console.warn,
        error: console.error,
        critical: console.error,
        getLogsUrl: () => 'Logger not configured - using console fallback'
      };
      console.warn('Using console fallback for logging.');
    }
  }
}

// Auto-initialize the logger when this file loads
initializeLogger();

/**
 * Optional manual setup function for the Apps Script editor.
 * The logger now configures itself automatically, so this is rarely needed.
 */
function configureLogger() {
  try {
    Logger.setup(GCP_PROJECT_ID, PropertiesService.getScriptProperties());
    console.log('Manual logger setup complete.');
    initializeLogger();
  } catch (error) {
    console.error('Failed to configure logger manually:', error.message);
    throw error;
  }
}
