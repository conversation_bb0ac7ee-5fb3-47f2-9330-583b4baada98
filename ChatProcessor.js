// Import necessary constants from Config.js and the global Log object from Logging.js
// In Google Apps Script, files in the same project are globally scoped,
// so direct access to functions and variables is possible without explicit imports.
// However, for clarity and maintainability, we'll assume these are available.

// Assuming isValidDateString is now in Utils.js and globally available
// const isValidDateString = Utils.isValidDateString;

/**
 * Groups an array of Google Chat Message objects by their explicit `thread.name`.
 * Messages without a `thread.name` are considered "orphans" and are returned separately.
 * Messages missing critical properties (`createTime`, `text`) or with invalid `createTime` are skipped.
 *
 * @param {Array<GoogleChat.Schema.Message>} messages An array of Google Chat Message objects.
 * @returns {{threadedConversations: Map<string, Array<GoogleChat.Schema.Message>>, orphans: Array<GoogleChat.Schema.Message>}} An object containing:
 *   - `threadedConversations`: A Map where keys are thread names and values are arrays of messages belonging to that thread.
 *   - `orphans`: An array of messages that do not belong to any explicit thread.
 */
function groupMessagesByThread(messages) {
  const threadedConversations = new Map();
  const orphans = [];

  messages.forEach(message => {
    // Validate essential message properties before processing
    if (!message || !message.createTime || !message.text || !isValidDateString(message.createTime)) {
      Log.warn(`Skipping message due to missing essential properties or invalid createTime: ${JSON.stringify(message)}`);
      return; // Skip this message if it's malformed
    }

    if (message.thread && message.thread.name) {
      const threadName = message.thread.name;
      if (!threadedConversations.has(threadName)) {
        threadedConversations.set(threadName, []);
      }
      threadedConversations.get(threadName).push(message);
    } else {
      orphans.push(message);
    }
  });

  return { threadedConversations, orphans };
}

/**
 * Groups orphaned (unthreaded) messages into logical conversations based on a maximum time gap.
 * Messages are sorted chronologically, and a new conversation block is started if the time
 * difference between consecutive messages exceeds `MAX_GAP_MINUTES`. Invalid messages are skipped.
 *
 * @param {Array<GoogleChat.Schema.Message>} orphans An array of unthreaded Google Chat Message objects.
 * @returns {Array<Array<GoogleChat.Schema.Message>>} An array of arrays, where each inner array represents
 *   a time-based conversation block of messages.
 */
function groupOrphansByTimeWindow(orphans) {
  if (orphans.length === 0) {
    return [];
  }

  // Sort orphans chronologically by their creation time
  orphans.sort((a, b) => {
    const timeA = Date.parse(a.createTime);
    const timeB = Date.parse(b.createTime);

    // Handle cases where createTime might be invalid after sorting
    if (isNaN(timeA) && isNaN(timeB)) return 0;
    if (isNaN(timeA)) return 1; // Push invalid dates to the end
    if (isNaN(timeB)) return -1; // Push invalid dates to the end

    return timeA - timeB;
  });

  const timeGroupedConversations = [];
  let currentConversation = [];
  let firstValidOrphanIndex = -1;

  // Find the first valid orphan message to start the initial conversation block
  for (let i = 0; i < orphans.length; i++) {
    if (isValidDateString(orphans[i].createTime)) {
      currentConversation.push(orphans[i]);
      firstValidOrphanIndex = i;
      break;
    } else {
      Log.warn(`Skipping initial orphan message due to invalid createTime: ${JSON.stringify(orphans[i])}`);
    }
  }

  if (currentConversation.length === 0) {
    return []; // No valid orphan messages found to process
  }

  // Iterate through the rest of the orphans to group them by time window
  for (let i = firstValidOrphanIndex + 1; i < orphans.length; i++) {
    const currentOrphan = orphans[i];
    if (!isValidDateString(currentOrphan.createTime)) {
      Log.warn(`Skipping orphan message due to invalid createTime during time grouping: ${JSON.stringify(currentOrphan)}`);
      continue; // Skip this invalid message and continue with the next
    }

    const prevMessageTimeMs = Date.parse(currentConversation[currentConversation.length - 1].createTime);
    const currentMessageTimeMs = Date.parse(currentOrphan.createTime);

    // If either timestamp is invalid, treat it as a large gap to start a new conversation
    if (isNaN(prevMessageTimeMs) || isNaN(currentMessageTimeMs)) {
      Log.warn(`Invalid time value encountered during time grouping. Starting new conversation block. Prev: ${currentConversation[currentConversation.length - 1].createTime}, Current: ${currentOrphan.createTime}`);
      timeGroupedConversations.push(currentConversation);
      currentConversation = [currentOrphan];
      continue;
    }

    const timeDifferenceMinutes = (currentMessageTimeMs - prevMessageTimeMs) / (1000 * 60);

    if (timeDifferenceMinutes <= MAX_GAP_MINUTES) {
      currentConversation.push(currentOrphan);
    } else {
      // If the gap is too large, start a new conversation block
      timeGroupedConversations.push(currentConversation);
      currentConversation = [currentOrphan];
    }
  }
  timeGroupedConversations.push(currentConversation); // Add the last conversation block

  return timeGroupedConversations;
}

/**
 * Processes an array of Google Chat messages, groups them into conversations (threaded or time-based),
 * chunks the conversation text, and then stores these chunks in Google Cloud Firestore.
 * This function is intended to be called by the main entry point in Code.js.
 *
 * @param {Array<GoogleChat.Schema.Message>} messages An array of Google Chat Message objects.
 * @param {string} [rawCollectionName=FIRESTORE_COLLECTION_NAME] The Firestore collection name for raw messages.
 * @param {string} [clusteredCollectionName=CLUSTERED_COLLECTION_NAME] The Firestore collection name for clustered summaries.
 * @returns {{clusteredSummaries: Array<Object>, saveReport: Object}} An object containing:
 *   - `clusteredSummaries`: An array of the generated cluster summary objects.
 *   - `saveReport`: A report detailing the success and failure of the Firestore save operation.
 */
function processChatHistoryAndStore(messages, rawCollectionName = FIRESTORE_COLLECTION_NAME, clusteredCollectionName = CLUSTERED_COLLECTION_NAME) {
  if (!messages || messages.length === 0) {
    Log.warn("No messages provided for processing.");
    return { clusteredSummaries: [], saveReport: { successful: 0, failed: 0, errors: [] } };
  }

  // Step 1: Retrieve already processed message IDs from Firestore.
  let processedMessageIds = new Set();
  try {
    processedMessageIds = getProcessedMessageIds(clusteredCollectionName); // Removed FirestoreService. prefix
    Log.info(`Retrieved ${processedMessageIds.size} already processed message IDs.`);
  } catch (e) {
    Log.error(`Failed to retrieve processed message IDs from Firestore: ${e.message}. Proceeding without filtering.`, { stack: e.stack });
    // If we can't get processed IDs, we'll proceed to process all messages,
    // which might lead to reprocessing but prevents blocking the entire operation.
  }

  // Filter out messages that have already been processed.
  const newMessages = messages.filter(message => {
    if (!message || !message.name) {
      Log.warn(`Skipping message with missing 'name' property during filtering: ${JSON.stringify(message)}`);
      return false; // Skip malformed messages
    }
    return !processedMessageIds.has(message.name);
  });

  if (newMessages.length === 0) {
    Log.info("All incoming messages have already been processed. No new messages to process.");
    return { clusteredSummaries: [], saveReport: { successful: 0, failed: 0, errors: [] } };
  }

  Log.info(`Processing ${newMessages.length} new messages (out of ${messages.length} total fetched).`);

  // Step 2: Prepare new messages for clustering.
  const messagesForClustering = newMessages.map(message => ({
    id: message.name,
    text: message.text || ''
  }));
  Log.info(`Prepared ${messagesForClustering.length} new messages for clustering.`);

  // Step 3: Perform large-scale topic clustering using the Gemini service on new messages.
  const clusteredResults = clusterLargeDataset(messagesForClustering);

  if (!clusteredResults || clusteredResults.length === 0) {
    Log.error("No clusters were generated by the Gemini service for new messages.");
    return { clusteredSummaries: [], saveReport: { successful: 0, failed: 0, errors: [] } };
  }
  Log.info(`Successfully generated ${clusteredResults.length} topic clusters from new messages.`);

  // Step 4: Generate a comprehensive summary for each cluster and prepare for storage.
  const clusteredSummaries = [];
  for (const cluster of clusteredResults) {
    // Retrieve the full text of messages belonging to this cluster (from the original 'messages' array for full context)
    const clusterMessages = newMessages.filter(msg => cluster.message_ids.includes(msg.name));
    const conversationText = clusterMessages.map(message => {
      const userDetails = getUserDetails(message.sender ? message.sender.name : null, new Map());
      const senderDisplayName = userDetails.displayName;
      const timestampMs = Date.parse(message.createTime);
      const timestamp = !isNaN(timestampMs) ? new Date(timestampMs).toISOString() : '';
      const text = message.text || '';
      return `${senderDisplayName} (${timestamp}): ${text}`;
    }).join('\n');

    // Summarize the entire cluster's conversation text
    let clusterSummary = "No summary generated.";
    try {
      clusterSummary = getGeminiSummaryForCluster_(conversationText, cluster.cluster_name);
      Log.debug(`Summarized cluster "${cluster.cluster_name}".`);
    } catch (e) {
      Log.error(`Failed to summarize cluster "${cluster.cluster_name}": ${e.message}`);
      clusterSummary = `Summarization failed for this cluster: ${e.message}`;
    }

    clusteredSummaries.push({
      cluster_id: cluster.cluster_id,
      cluster_name: cluster.cluster_name,
      summary: clusterSummary,
      message_ids: cluster.message_ids,
      message_count: cluster.message_ids.length,
      summarizedAt: new Date().toISOString()
    });
  }
  Log.info(`Generated summaries for ${clusteredSummaries.length} new clusters.`);

  // Step 5: Save the clustered summaries to Firestore.
  const saveReport = saveChunksToFirestore(clusteredSummaries, clusteredCollectionName);
  Log.info(`Clustered summaries save report: Successful: ${saveReport.successful}, Failed: ${saveReport.failed}`);

  return {
    clusteredSummaries: clusteredSummaries,
    saveReport: saveReport
  };
}

/**
 * Fetches a summary for a given text chunk using the Google Gemini API.
 * This function is specifically for summarizing a *cluster* of messages.
 *
 * @param {string} text The concatenated text content of the cluster to be summarized.
 * @param {string} clusterName The name of the cluster to include in the prompt.
 * @param {number} [maxInputTokens=MAX_SUMMARY_INPUT_TOKENS] The maximum input tokens for a single API call.
 * @param {number} [maxOutputTokens=MAX_SUMMARY_OUTPUT_TOKENS] The maximum output tokens for the summary.
 * @returns {string} The summarized text.
 * @throws {Error} If the API call fails or returns an error.
 * @private
 */
function getGeminiSummaryForCluster_(text, clusterName, maxInputTokens = MAX_SUMMARY_INPUT_TOKENS, maxOutputTokens = MAX_SUMMARY_OUTPUT_TOKENS) {
  const API_KEY = GEMINI_API_KEY;
  if (!API_KEY) {
    Log.error('Error: GEMINI_API_KEY not found in Config.js.');
    throw new Error('GEMINI_API_KEY not found.');
  }

  const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${API_KEY}`;

  // Helper to estimate tokens (very rough, actual tokenization is complex)
  // A token is roughly 4 characters for Gemini models.
  const estimateTokens = (str) => Math.ceil(str.length / 4);

  // If the text is too long, recursively summarize in chunks
  if (estimateTokens(text) > maxInputTokens) {
    Log.info(`Conversation text for cluster "${clusterName}" is too long (${estimateTokens(text)} tokens). Chunking for recursive summarization.`);
    const chunks = chunkText_(text, maxInputTokens); // Assuming _chunkText is available or will be added
    const subSummaries = [];

    for (let i = 0; i < chunks.length; i++) {
      Log.debug(`Summarizing sub-chunk ${i + 1}/${chunks.length} for cluster "${clusterName}".`);
      try {
        // Recursively summarize each chunk, passing a smaller output token limit for sub-summaries
        const subSummary = getGeminiSummaryForCluster_(
          chunks[i],
          `${clusterName} (Part ${i + 1})`,
          maxInputTokens,
          Math.floor(maxOutputTokens / chunks.length) || 100 // Distribute output tokens or set a minimum
        );
        subSummaries.push(subSummary);
      } catch (e) {
        Log.error(`Failed to summarize sub-chunk ${i + 1} for cluster "${clusterName}": ${e.message}`);
        subSummaries.push(`[Error summarizing part ${i + 1}: ${e.message}]`);
      }
    }

    const combinedSubSummaries = subSummaries.join('\n\n');
    Log.info(`Summarizing combined sub-summaries for cluster "${clusterName}".`);
    // Summarize the combined sub-summaries to get the final summary
    return getGeminiSummaryForCluster_(
      combinedSubSummaries,
      `Overall Summary for ${clusterName}`,
      maxInputTokens,
      maxOutputTokens
    );
  }

  const promptText = `
    Summarize the following conversation, which is part of the topic "${clusterName}".
    Focus on key decisions, action items, and important discussions related to this topic.
    Keep the summary concise and to the point, suitable for quick reference.

    Conversation Text:
    ${text}
  `;

  const payload = {
    contents: [{
      parts: [{
        text: promptText
      }]
    }],
    generationConfig: {
      temperature: 0.2,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: maxOutputTokens // Control output length
    }
  };

  const options = {
    method: 'POST',
    contentType: 'application/json',
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  };

  try {
    const response = UrlFetchApp.fetch(API_URL, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    if (responseCode >= 200 && responseCode < 300) {
      const responseJson = JSON.parse(responseText);
      if (responseJson.candidates && responseJson.candidates.length > 0 &&
        responseJson.candidates[0].content && responseJson.candidates[0].content.parts &&
        responseJson.candidates[0].content.parts.length > 0 && responseJson.candidates[0].content.parts[0].text) {
        return responseJson.candidates[0].content.parts[0].text;
      } else {
        const errorMessage = "Gemini API response did not contain expected summary text or was malformed.";
        Log.warn(errorMessage, { response: responseJson, textPreview: text.substring(0, 100) });
        throw new Error(errorMessage);
      }
    } else {
      const errorMessage = `Gemini API Error: ${responseCode} - ${responseText}`;
      Log.error(errorMessage, { responseCode: responseCode, responseText: responseText, textPreview: text.substring(0, 200) });
      throw new Error(errorMessage);
    }
  } catch (e) {
    Log.error(`Exception calling Gemini API for cluster summarization: ${e.message}`, { stack: e.stack, textPreview: text.substring(0, 200) });
    throw new Error(`Failed to get summary from AI model: ${e.message}`);
  }
}

/**
 * Chunks a long text string into smaller pieces based on a maximum token estimate.
 * This is a simple character-based chunking, assuming 4 characters per token.
 * For more precise tokenization, a dedicated tokenizer library would be needed.
 *
 * @param {string} text The input text to chunk.
 * @param {number} maxTokens The maximum estimated tokens per chunk.
 * @returns {Array<string>} An array of text chunks.
 * @private
 */
function chunkText_(text, maxTokens) {
  // Helper to estimate tokens (very rough, actual tokenization is complex)
  // A token is roughly 4 characters for Gemini models.
  const estimateTokens = (str) => Math.ceil(str.length / 4);

  const maxChars = maxTokens * 4; // Rough estimate: 1 token = 4 characters
  const chunks = [];
  let currentChunk = '';
  const sentences = text.split(/(?<=[.!?])\s+/); // Split by sentence-ending punctuation followed by space

  for (const sentence of sentences) {
    if (estimateTokens(currentChunk + sentence) <= maxTokens) {
      currentChunk += (currentChunk ? ' ' : '') + sentence;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk);
      }
      currentChunk = sentence;
    }
  }
  if (currentChunk) {
    chunks.push(currentChunk);
  }

  // Fallback for very long sentences that exceed maxTokens
  if (chunks.length === 0 && text.length > 0) {
    for (let i = 0; i < text.length; i += maxChars) {
      chunks.push(text.substring(i, i + maxChars));
    }
  } else if (chunks.length === 1 && estimateTokens(chunks[0]) > maxTokens) {
    // If a single chunk is still too large (e.g., one very long sentence), split by character limit
    const finalChunks = [];
    for (let i = 0; i < chunks[0].length; i += maxChars) {
      finalChunks.push(chunks[0].substring(i, i + maxChars));
    }
    return finalChunks;
  }

  return chunks;
}
