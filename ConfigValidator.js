/**
 * Configuration Validation and Management
 * 
 * This module provides centralized configuration validation and management
 * to prevent runtime errors due to missing or invalid configuration values.
 */

/**
 * Validates all required configuration values are present and valid
 * @returns {Object} Validation result with success flag and any errors
 */
function validateConfiguration() {
  const errors = [];
  const warnings = [];

  // Required configurations
  const requiredConfigs = [
    { name: 'GEMINI_API_KEY', value: GEMINI_API_KEY, type: 'string', minLength: 30 },
    { name: 'GEMINI_MODEL', value: GEMINI_MODEL, type: 'string', minLength: 5 },
    { name: 'FIRESTORE_PROJECT_ID', value: FIRESTORE_PROJECT_ID, type: 'string', minLength: 5 },
    { name: 'SPACE_NAME', value: SPACE_NAME, type: 'string', minLength: 10 },
    { name: 'FIRESTORE_COLLECTION_NAME', value: FIRESTORE_COLLECTION_NAME, type: 'string', minLength: 3 },
    { name: 'CLUSTERED_COLLECTION_NAME', value: CLUSTERED_COLLECTION_NAME, type: 'string', minLength: 3 }
  ];

  // Numeric configurations
  const numericConfigs = [
    { name: 'MAX_SUMMARY_INPUT_TOKENS', value: MAX_SUMMARY_INPUT_TOKENS, min: 1000, max: 2000000 },
    { name: 'MAX_SUMMARY_OUTPUT_TOKENS', value: MAX_SUMMARY_OUTPUT_TOKENS, min: 100, max: 100000 },
    { name: 'MAX_MESSAGES_PER_CHUNK', value: MAX_MESSAGES_PER_CHUNK, min: 10, max: 1000 },
    { name: 'LOOKBACK_DAYS', value: LOOKBACK_DAYS, min: 1, max: 365 },
    { name: 'MAX_GAP_MINUTES', value: MAX_GAP_MINUTES, min: 1, max: 1440 }
  ];

  // Validate required string configurations
  requiredConfigs.forEach(config => {
    if (!config.value || typeof config.value !== config.type) {
      errors.push(`${config.name} is missing or not a ${config.type}`);
    } else if (config.value.length < config.minLength) {
      errors.push(`${config.name} is too short (minimum ${config.minLength} characters)`);
    }
  });

  // Validate numeric configurations
  numericConfigs.forEach(config => {
    if (typeof config.value !== 'number' || isNaN(config.value)) {
      errors.push(`${config.name} must be a valid number`);
    } else if (config.value < config.min || config.value > config.max) {
      errors.push(`${config.name} must be between ${config.min} and ${config.max}`);
    }
  });

  // Validate API key format (should start with AIza for Google APIs)
  if (GEMINI_API_KEY && !GEMINI_API_KEY.startsWith('AIza')) {
    warnings.push('GEMINI_API_KEY does not appear to be a valid Google API key format');
  }

  // Validate model name format
  if (GEMINI_MODEL && !GEMINI_MODEL.includes('gemini')) {
    warnings.push('GEMINI_MODEL does not appear to be a valid Gemini model name');
  }

  // Validate space name format
  if (SPACE_NAME && !SPACE_NAME.startsWith('spaces/')) {
    errors.push('SPACE_NAME must start with "spaces/"');
  }

  return {
    success: errors.length === 0,
    errors: errors,
    warnings: warnings,
    summary: `Configuration validation: ${errors.length} errors, ${warnings.length} warnings`
  };
}

/**
 * Gets the current configuration as a safe object (without sensitive data)
 * @returns {Object} Configuration object with sensitive values masked
 */
function getConfigurationSummary() {
  return {
    GEMINI_MODEL: GEMINI_MODEL,
    GEMINI_API_KEY_LENGTH: GEMINI_API_KEY ? GEMINI_API_KEY.length : 0,
    GEMINI_API_KEY_PREFIX: GEMINI_API_KEY ? GEMINI_API_KEY.substring(0, 8) + '...' : 'NOT_SET',
    FIRESTORE_PROJECT_ID: FIRESTORE_PROJECT_ID,
    SPACE_NAME: SPACE_NAME,
    COLLECTIONS: {
      RAW: FIRESTORE_COLLECTION_NAME,
      CLUSTERED: CLUSTERED_COLLECTION_NAME,
      TEST_RAW: TEST_FIRESTORE_COLLECTION_NAME,
      TEST_CLUSTERED: TEST_CLUSTERED_COLLECTION_NAME,
      METADATA: METADATA_COLLECTION_NAME
    },
    LIMITS: {
      MAX_SUMMARY_INPUT_TOKENS: MAX_SUMMARY_INPUT_TOKENS,
      MAX_SUMMARY_OUTPUT_TOKENS: MAX_SUMMARY_OUTPUT_TOKENS,
      MAX_MESSAGES_PER_CHUNK: MAX_MESSAGES_PER_CHUNK,
      LOOKBACK_DAYS: LOOKBACK_DAYS,
      MAX_GAP_MINUTES: MAX_GAP_MINUTES,
      FIRESTORE_BATCH_SIZE: FIRESTORE_BATCH_SIZE
    }
  };
}

/**
 * Validates configuration and throws an error if invalid
 * Call this at the start of critical functions
 */
function ensureValidConfiguration() {
  const validation = validateConfiguration();
  if (!validation.success) {
    const errorMessage = `Configuration validation failed: ${validation.errors.join(', ')}`;
    Log.error(errorMessage);
    throw new Error(errorMessage);
  }
  
  if (validation.warnings.length > 0) {
    Log.warn(`Configuration warnings: ${validation.warnings.join(', ')}`);
  }
}
