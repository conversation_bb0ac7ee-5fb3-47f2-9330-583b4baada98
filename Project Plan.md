# Project Plan: Chat Space Summarizer

This document outlines the development plan for the Google Apps Script project, which has evolved to use a sophisticated clustering model for processing chat histories.

## Phase 1: Core Logic and Data Structuring (Completed)

**Objective:** Implement and test the core data processing logic for grouping messages by thread and time.
**Status:** Completed.

## Phase 2: Firestore Integration (Completed)

**Objective:** Implement the logic to save data to Google Cloud Firestore using a custom library for secure authentication.
**Status:** Completed.

## Phase 3: Advanced Topic Clustering (Current Phase)

**Objective:** Refactor the core processing logic to use a Map-Reduce pattern with the Gemini API for advanced, scalable topic clustering. This replaces the previous, simplistic summarization-per-chunk approach.

**Detailed Steps:**

1.  **Refactor `GeminiService.js`:**
    *   Implement the main orchestration function `clusterLargeDataset(messages, chunkSize)` which will manage the Map-Reduce workflow.
    *   Create a private "Map" function `_clusterSingleChunk(messageChunk)` that calls the Gemini API with a specific JSON schema to get locally-scoped topic clusters from a small chunk of messages.
    *   Create a private "Reduce" function `_consolidateClusterTopics(topicNames)` that calls the Gemini API to analyze all unique topic names generated in the Map phase and consolidate them into a canonical list (e.g., merging "Billing Issues" and "Payment Problems" into "Billing & Payments").
    *   Remove the now-obsolete `summarizeChunks` and `getGeminiSummary` functions.

2.  **Refactor `ChatProcessor.js`:**
    *   Update the main processing function (`processChatHistoryAndStore`) to drive the new workflow.
    *   Remove the old text consolidation and manual chunking logic (`consolidateAndChunk`, `splitTextIntoChunks`).
    *   The function will first prepare all messages into a simple `[{ id, text }]` array.
    *   It will then invoke `GeminiService.clusterLargeDataset()` to receive the final, globally consistent topic clusters.
    *   **Post-processing:** After receiving the clusters, the processor will:
        *   Iterate through each final cluster.
        *   For each cluster, generate a new, comprehensive summary based on all its constituent messages.
        *   Save each cluster as a single, well-structured document in Firestore, containing the canonical cluster name, the full summary, and the array of associated message IDs.

## Phase 4: Testing and Validation

**Objective:** Thoroughly test the new clustering and summarization pipeline.

1.  **Unit Testing:** Create test functions in `_Tests.js` to validate the `GeminiService` helpers, mocking the API responses.
2.  **End-to-End Testing:** Execute a full run of `processChatHistoryAndStore` using a sample of real data to ensure the Map-Reduce process works as expected and the final data is correctly structured and saved in Firestore.

## Alternatives Considered

### Python Backend with LangExtract Library

An alternative architecture was considered that would involve creating a separate Python backend to leverage Google's `LangExtract` library. This remains a viable future evolution for the project if requirements become more complex.

*   **Description:** `LangExtract` is a powerful, open-source Python library specifically designed for high-quality information extraction from large volumes of text. It provides a high-level interface to LLMs like Gemini and has several key advantages.

*   **Proposed Implementation Details:**
    1.  **Backend Service:** A new Google Cloud Function would be created using the Python 3.x runtime. This function would house the core logic.
    2.  **API Endpoint:** The Cloud Function would be triggered via HTTP, exposing a secure endpoint that the Google Apps Script project could call.
    3.  **Core Logic:**
        *   The Python function would receive a payload containing the raw array of chat messages from the Apps Script client.
        *   It would use the `langextract` library to process the messages. The prompt and few-shot examples would be defined within the Python code, similar to the library's documentation.
        *   The `lx.extract()` function would be called to perform the clustering and entity extraction.
        *   The function would return a structured JSON object containing the extracted clusters, complete with source-grounded text spans and character offsets.
    4.  **Authentication:** The Google Cloud Function would be secured using IAM to only allow invocations from the Apps Script project's associated service account, ensuring a secure connection between the two services.
    5.  **Apps Script Role:** The Apps Script code would be simplified to only be responsible for fetching the chat history and then making a single, authenticated `UrlFetchApp.fetch()` call to the new Python Cloud Function endpoint. It would then receive and store the final, processed JSON.

*   **Advantages:**
    *   **Superior Long-Context Handling:** The library has built-in, optimized strategies for handling documents that exceed a model's context window, including intelligent chunking and parallel processing.
    *   **Precise Source Grounding:** A core feature of LangExtract is its ability to map every piece of extracted information back to the exact character offsets in the source text, providing excellent traceability and making verification easier.
    *   **Simplified Interface:** It abstracts away much of the low-level prompt engineering and API call complexity.
    *   **Python Ecosystem:** Unlocks the broader Python data science and ML ecosystem for any future analysis needs.

*   **Disadvantages:**
    *   **Architectural Complexity:** This approach requires developing, deploying, and maintaining a separate Python service. The Google Apps Script project would then need to make authenticated calls to this new service, adding another layer of infrastructure and potential points of failure.
    *   **Increased Overhead:** It introduces a new programming language and deployment pipeline into the project, increasing the maintenance burden.

*   **Decision:** While `LangExtract` offers a more powerful and specialized toolset, the proposed Map-Reduce implementation within Google Apps Script provides a sufficiently robust and scalable solution for the project's current goals without introducing significant architectural complexity. This approach delivers the desired thematic clustering and long-context handling capabilities directly within the existing environment, making it the more pragmatic choice.
