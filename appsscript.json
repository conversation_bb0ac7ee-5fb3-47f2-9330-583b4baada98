{"timeZone": "America/Los_Angeles", "exceptionLogging": "STACKDRIVER", "runtimeVersion": "V8", "dependencies": {"libraries": [{"userSymbol": "FirestoreApp", "libraryId": "1WyoVOxSyrRQALgOzTFD_0XLiGfMhoV06rGqQGHKd8Ljen9o99mvxVa91", "version": "0"}, {"userSymbol": "<PERSON><PERSON>", "libraryId": "1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u", "version": "9"}], "enabledAdvancedServices": [{"userSymbol": "Cha<PERSON>", "version": "v1", "serviceId": "chat"}, {"userSymbol": "People", "version": "v1", "serviceId": "<PERSON><PERSON><PERSON>"}]}, "oauthScopes": ["https://www.googleapis.com/auth/iam", "https://www.googleapis.com/auth/script.external_request", "https://www.googleapis.com/auth/datastore", "https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/chat.messages.readonly", "https://www.googleapis.com/auth/documents"], "executionApi": {"access": "ANYONE"}}