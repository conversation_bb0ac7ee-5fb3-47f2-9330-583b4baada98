# Technical Context

This document details the technologies used, development setup, technical constraints, dependencies, and tool usage patterns for the "Chat Space Summarizer" project.

## Technologies Used

*   **Frontend:** Google Apps Script (HTML Service, JavaScript, CSS).
*   **Backend:** Google Apps Script (JavaScript).
*   **Database:** Google Cloud Firestore.
*   **Authentication:** Google OAuth2 (via Apps Script's native capabilities and service account impersonation).
*   **APIs:** Google Cloud Logging API (via custom library), Google Cloud IAM Credentials API (for token generation).

## Development Setup

*   **Google Apps Script:** Development is done directly within the Google Apps Script editor or locally using `clasp` (Command Line Apps Script Project).
*   **`clasp`:** Used for pushing and pulling code between the local environment and Google Apps Script.
*   **VS Code:** Primary IDE for local development.

## Technical Constraints

*   **Google Apps Script Limitations:** Execution time limits, memory limits, and specific API quotas.
*   **Firestore Quotas:** Standard Firestore read/write/delete quotas apply.
*   **Security Policies:** Adherence to Google Cloud security best practices, including avoiding static service account keys.

## Dependencies

*   **Custom Firestore Library:** A modified version of `grahamearley/FirestoreGoogleAppsScript` that accepts dynamically generated access tokens. (Library ID: `1WyoVOxSyrRQALgOzTFD_0XLiGfMhoV06rGqQGHKd8Ljen9o99mvxVa91`)
*   **Structured Logging Library:** A custom Google Apps Script library for structured logging to Google Cloud Logging. (Library ID: `1a2pKpnH20246miqgwW3F1SYSaxxQBnXPzSIxf0tdmWd1bWIzQ5-WHD6u`)

## Tool Usage Patterns

*   **`clasp push` / `clasp pull`:** For synchronizing local code with Apps Script.
*   **`read_file` / `write_to_file` / `replace_in_file`:** For managing code files.
*   **`execute_command`:** For `clasp` operations and other CLI tasks.
*   **`browser_action`:** For testing the Apps Script web app in a browser.
